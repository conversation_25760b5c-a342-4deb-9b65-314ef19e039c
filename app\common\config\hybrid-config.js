// 混合模式配置文件
export default {
  // Web端配置
  web: {
    // 生产环境URL
    productionUrl: 'https://your-production-domain.com',
    // 开发环境URL
    developmentUrl: 'https://your-dev-domain.com',
    // 本地开发URL
    localUrl: 'http://localhost:8080',
    
    // 当前使用的URL（根据环境自动选择）
    get currentUrl() {
      // 可以根据环境变量或其他条件来选择URL
      if (process.env.NODE_ENV === 'production') {
        return this.productionUrl
      } else if (process.env.NODE_ENV === 'development') {
        return this.developmentUrl
      } else {
        return this.localUrl
      }
    }
  },
  
  // 屏幕检测配置
  screen: {
    // 大屏宽度阈值（像素）
    largeScreenWidth: 768,
    // 宽高比阈值
    aspectRatioThreshold: 1.2,
    // 检测延迟（毫秒）
    detectionDelay: 100
  },
  
  // 模式配置
  mode: {
    // 默认模式：'auto', 'web', 'native'
    default: 'auto',
    // 是否启用调试模式
    debug: false,
    // 是否允许用户手动切换模式
    allowManualSwitch: true
  },
  
  // URL参数配置
  urlParams: {
    // 固定参数
    fixed: {
      from: 'miniprogram',
      platform: 'wechat',
      version: '1.0.5'
    },
    // 动态参数（会根据当前状态添加）
    dynamic: [
      'screenWidth',
      'screenHeight',
      'pixelRatio',
      'statusBarHeight',
      'safeAreaTop',
      'safeAreaBottom'
    ]
  },
  
  // 页面配置
  pages: {
    // 首页
    index: {
      webPath: '/',
      nativePath: '/pages/index/index',
      title: '首页',
      enableHybrid: true
    },
    // 搜索页
    search: {
      webPath: '/search',
      nativePath: '/pages/search/search',
      title: '搜索',
      enableHybrid: true
    },
    // 用户页
    user: {
      webPath: '/user',
      nativePath: '/pages/user/user',
      title: '我的',
      enableHybrid: false // 用户页可能涉及敏感信息，不启用web端
    },
    // 练习页
    practice: {
      webPath: '/practice',
      nativePath: '/pages/practice/practice',
      title: '练习',
      enableHybrid: true
    }
  },
  
  // 消息通信配置
  message: {
    // 允许的消息类型
    allowedTypes: [
      'navigation',
      'userAction',
      'dataSync',
      'error',
      'ready'
    ],
    // 消息超时时间（毫秒）
    timeout: 5000
  },
  
  // 错误处理配置
  error: {
    // 最大重试次数
    maxRetries: 3,
    // 重试间隔（毫秒）
    retryInterval: 1000,
    // 是否显示错误提示
    showErrorToast: true,
    // 错误回退策略：'native', 'reload', 'redirect'
    fallbackStrategy: 'native'
  },
  
  // 性能配置
  performance: {
    // 预加载web端
    preloadWeb: false,
    // 缓存策略
    cacheStrategy: 'default',
    // 超时时间（毫秒）
    loadTimeout: 10000
  },
  
  // 获取页面配置
  getPageConfig(pageName) {
    return this.pages[pageName] || null
  },
  
  // 获取完整的web URL
  getWebUrl(pageName, params = {}) {
    const pageConfig = this.getPageConfig(pageName)
    if (!pageConfig || !pageConfig.enableHybrid) {
      return null
    }
    
    const baseUrl = this.web.currentUrl
    const webPath = pageConfig.webPath
    const url = new URL(webPath, baseUrl)
    
    // 添加固定参数
    Object.keys(this.urlParams.fixed).forEach(key => {
      url.searchParams.set(key, this.urlParams.fixed[key])
    })
    
    // 添加自定义参数
    Object.keys(params).forEach(key => {
      url.searchParams.set(key, params[key])
    })
    
    return url.toString()
  },
  
  // 检查是否应该使用web端
  shouldUseWeb(screenInfo, pageName, forceMode = 'auto') {
    if (forceMode === 'web') return true
    if (forceMode === 'native') return false
    
    const pageConfig = this.getPageConfig(pageName)
    if (!pageConfig || !pageConfig.enableHybrid) {
      return false
    }
    
    const { windowWidth = 0, windowHeight = 1 } = screenInfo
    const aspectRatio = windowWidth / windowHeight
    
    return windowWidth > this.screen.largeScreenWidth || 
           aspectRatio > this.screen.aspectRatioThreshold
  }
}
