/**
 * 响应式CSS样式
 * 基于微信官方大屏适配指南
 */

/* 基础容器 */
.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 15rpx;
}

/* 断点容器 */
.breakpoint-xs .responsive-container { max-width: 100%; }
.breakpoint-sm .responsive-container { max-width: 540px; }
.breakpoint-md .responsive-container { max-width: 720px; }
.breakpoint-lg .responsive-container { max-width: 960px; }
.breakpoint-xl .responsive-container { max-width: 1140px; }
.breakpoint-xxl .responsive-container { max-width: 1320px; }

/* 栅格系统 */
.responsive-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -5rpx;
}

.responsive-grid-item {
  padding: 5rpx;
  box-sizing: border-box;
}

/* 栅格列 */
.breakpoint-xs .grid-cols-1 .responsive-grid-item { width: 100%; }
.breakpoint-sm .grid-cols-2 .responsive-grid-item { width: 50%; }
.breakpoint-md .grid-cols-3 .responsive-grid-item { width: 33.333%; }
.breakpoint-lg .grid-cols-4 .responsive-grid-item { width: 25%; }
.breakpoint-xl .grid-cols-5 .responsive-grid-item { width: 20%; }
.breakpoint-xxl .grid-cols-6 .responsive-grid-item { width: 16.666%; }

/* 布局模式 */
.layout-mobile {
  flex-direction: column;
}

.layout-tablet {
  flex-direction: row;
  flex-wrap: wrap;
}

.layout-desktop {
  flex-direction: row;
  flex-wrap: nowrap;
}

/* 侧边栏布局 */
.with-sidebar {
  display: flex;
}

.responsive-sidebar {
  flex-shrink: 0;
  background: #f8f9fa;
  border-right: 1rpx solid #e9ecef;
}

.responsive-main {
  flex: 1;
  min-width: 0;
}

.breakpoint-xs .responsive-sidebar,
.breakpoint-sm .responsive-sidebar {
  display: none;
}

.breakpoint-md .responsive-sidebar {
  width: 200px;
}

.breakpoint-lg .responsive-sidebar,
.breakpoint-xl .responsive-sidebar,
.breakpoint-xxl .responsive-sidebar {
  width: 240px;
}

/* 卡片布局 */
.responsive-card {
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  overflow: hidden;
  margin-bottom: 20rpx;
}

.large-screen .responsive-card {
  margin-bottom: 24rpx;
}

/* 列表布局 */
.responsive-list {
  display: flex;
  flex-direction: column;
}

.layout-tablet .responsive-list,
.layout-desktop .responsive-list {
  flex-direction: row;
  flex-wrap: wrap;
}

.responsive-list-item {
  width: 100%;
  margin-bottom: 20rpx;
}

.breakpoint-md .responsive-list-item {
  width: calc(50% - 10rpx);
  margin-right: 20rpx;
}

.breakpoint-lg .responsive-list-item {
  width: calc(33.333% - 14rpx);
  margin-right: 20rpx;
}

.breakpoint-xl .responsive-list-item {
  width: calc(25% - 15rpx);
  margin-right: 20rpx;
}

.breakpoint-xxl .responsive-list-item {
  width: calc(20% - 16rpx);
  margin-right: 20rpx;
}

/* 清除最后一个元素的右边距 */
.responsive-list-item:nth-child(2n) {
  margin-right: 0;
}

.breakpoint-lg .responsive-list-item:nth-child(2n) {
  margin-right: 20rpx;
}

.breakpoint-lg .responsive-list-item:nth-child(3n) {
  margin-right: 0;
}

.breakpoint-xl .responsive-list-item:nth-child(3n) {
  margin-right: 20rpx;
}

.breakpoint-xl .responsive-list-item:nth-child(4n) {
  margin-right: 0;
}

.breakpoint-xxl .responsive-list-item:nth-child(4n) {
  margin-right: 20rpx;
}

.breakpoint-xxl .responsive-list-item:nth-child(5n) {
  margin-right: 0;
}

/* 响应式字体 */
.responsive-text-xs { font-size: 24rpx; }
.responsive-text-sm { font-size: 28rpx; }
.responsive-text-base { font-size: 32rpx; }
.responsive-text-lg { font-size: 36rpx; }
.responsive-text-xl { font-size: 40rpx; }
.responsive-text-2xl { font-size: 48rpx; }

.large-screen .responsive-text-xs { font-size: 26rpx; }
.large-screen .responsive-text-sm { font-size: 30rpx; }
.large-screen .responsive-text-base { font-size: 34rpx; }
.large-screen .responsive-text-lg { font-size: 38rpx; }
.large-screen .responsive-text-xl { font-size: 42rpx; }
.large-screen .responsive-text-2xl { font-size: 52rpx; }

/* 响应式间距 */
.responsive-padding-xs { padding: 10rpx; }
.responsive-padding-sm { padding: 15rpx; }
.responsive-padding-base { padding: 20rpx; }
.responsive-padding-lg { padding: 30rpx; }
.responsive-padding-xl { padding: 40rpx; }

.large-screen .responsive-padding-xs { padding: 12rpx; }
.large-screen .responsive-padding-sm { padding: 18rpx; }
.large-screen .responsive-padding-base { padding: 24rpx; }
.large-screen .responsive-padding-lg { padding: 36rpx; }
.large-screen .responsive-padding-xl { padding: 48rpx; }

.responsive-margin-xs { margin: 10rpx; }
.responsive-margin-sm { margin: 15rpx; }
.responsive-margin-base { margin: 20rpx; }
.responsive-margin-lg { margin: 30rpx; }
.responsive-margin-xl { margin: 40rpx; }

.large-screen .responsive-margin-xs { margin: 12rpx; }
.large-screen .responsive-margin-sm { margin: 18rpx; }
.large-screen .responsive-margin-base { margin: 24rpx; }
.large-screen .responsive-margin-lg { margin: 36rpx; }
.large-screen .responsive-margin-xl { margin: 48rpx; }

/* 隐藏/显示工具类 */
.hidden-xs { display: none !important; }
.breakpoint-sm .hidden-xs { display: block !important; }

.hidden-sm { display: block !important; }
.breakpoint-sm .hidden-sm { display: none !important; }
.breakpoint-md .hidden-sm { display: block !important; }

.hidden-md { display: block !important; }
.breakpoint-md .hidden-md { display: none !important; }
.breakpoint-lg .hidden-md { display: block !important; }

.hidden-lg { display: block !important; }
.breakpoint-lg .hidden-lg { display: none !important; }
.breakpoint-xl .hidden-lg { display: block !important; }

.hidden-xl { display: block !important; }
.breakpoint-xl .hidden-xl { display: none !important; }
.breakpoint-xxl .hidden-xl { display: block !important; }

.hidden-xxl { display: block !important; }
.breakpoint-xxl .hidden-xxl { display: none !important; }

/* 只在指定断点显示 */
.visible-xs { display: block !important; }
.breakpoint-sm .visible-xs { display: none !important; }

.visible-sm { display: none !important; }
.breakpoint-sm .visible-sm { display: block !important; }
.breakpoint-md .visible-sm { display: none !important; }

.visible-md { display: none !important; }
.breakpoint-md .visible-md { display: block !important; }
.breakpoint-lg .visible-md { display: none !important; }

.visible-lg { display: none !important; }
.breakpoint-lg .visible-lg { display: block !important; }
.breakpoint-xl .visible-lg { display: none !important; }

.visible-xl { display: none !important; }
.breakpoint-xl .visible-xl { display: block !important; }
.breakpoint-xxl .visible-xl { display: none !important; }

.visible-xxl { display: none !important; }
.breakpoint-xxl .visible-xxl { display: block !important; }

/* 弹性布局工具 */
.responsive-flex {
  display: flex;
}

.responsive-flex-column {
  flex-direction: column;
}

.responsive-flex-row {
  flex-direction: row;
}

.layout-tablet .responsive-flex-column-mobile {
  flex-direction: row;
}

.layout-desktop .responsive-flex-column-mobile {
  flex-direction: row;
}

/* 按钮响应式 */
.responsive-button {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.large-screen .responsive-button {
  padding: 24rpx 48rpx;
  font-size: 34rpx;
}

/* 输入框响应式 */
.responsive-input {
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.large-screen .responsive-input {
  padding: 24rpx;
  font-size: 34rpx;
}
