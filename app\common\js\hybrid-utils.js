import hybridConfig from '@/common/config/hybrid-config.js'

/**
 * 混合模式工具类
 */
class HybridUtils {
  constructor() {
    this.config = hybridConfig
    this.screenInfo = null
    this.currentMode = 'auto'
    this.listeners = new Map()
  }
  
  /**
   * 初始化
   */
  init() {
    this.updateScreenInfo()
    this.setupEventListeners()
  }
  
  /**
   * 更新屏幕信息
   */
  updateScreenInfo() {
    try {
      this.screenInfo = uni.getSystemInfoSync()
      console.log('屏幕信息更新:', this.screenInfo)
      this.emit('screenInfoUpdate', this.screenInfo)
    } catch (error) {
      console.error('获取屏幕信息失败:', error)
    }
  }
  
  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听页面显示事件
    uni.onAppShow(() => {
      this.updateScreenInfo()
    })
    
    // 监听窗口尺寸变化（如果支持）
    if (typeof uni.onWindowResize === 'function') {
      uni.onWindowResize(() => {
        setTimeout(() => {
          this.updateScreenInfo()
        }, this.config.screen.detectionDelay)
      })
    }
  }
  
  /**
   * 判断是否应该使用web端
   * @param {string} pageName 页面名称
   * @param {string} forceMode 强制模式
   * @returns {boolean}
   */
  shouldUseWeb(pageName, forceMode = 'auto') {
    if (!this.screenInfo) {
      this.updateScreenInfo()
    }
    
    return this.config.shouldUseWeb(this.screenInfo, pageName, forceMode)
  }
  
  /**
   * 获取页面URL
   * @param {string} pageName 页面名称
   * @param {object} params 额外参数
   * @returns {string|null}
   */
  getPageUrl(pageName, params = {}) {
    // 添加屏幕信息到参数
    if (this.screenInfo) {
      this.config.urlParams.dynamic.forEach(key => {
        if (this.screenInfo[key] !== undefined) {
          params[key] = this.screenInfo[key]
        }
      })
    }
    
    return this.config.getWebUrl(pageName, params)
  }
  
  /**
   * 获取原生页面路径
   * @param {string} pageName 页面名称
   * @returns {string|null}
   */
  getNativePath(pageName) {
    const pageConfig = this.config.getPageConfig(pageName)
    return pageConfig ? pageConfig.nativePath : null
  }
  
  /**
   * 导航到页面（自动选择web或原生）
   * @param {string} pageName 页面名称
   * @param {object} options 导航选项
   */
  navigateTo(pageName, options = {}) {
    const { params = {}, forceMode = 'auto', navigationType = 'navigateTo' } = options
    
    if (this.shouldUseWeb(pageName, forceMode)) {
      // 使用web端
      const webUrl = this.getPageUrl(pageName, params)
      if (webUrl) {
        uni[navigationType]({
          url: `/pages/webview/webview?webUrl=${encodeURIComponent(webUrl)}&pageName=${pageName}`
        })
        return
      }
    }
    
    // 使用原生页面
    const nativePath = this.getNativePath(pageName)
    if (nativePath) {
      const queryString = Object.keys(params).map(key => 
        `${key}=${encodeURIComponent(params[key])}`
      ).join('&')
      
      const url = queryString ? `${nativePath}?${queryString}` : nativePath
      uni[navigationType]({ url })
    } else {
      console.error(`页面 ${pageName} 未找到对应的原生路径`)
    }
  }
  
  /**
   * 处理web端消息
   * @param {object} message 消息对象
   */
  handleWebMessage(message) {
    const { type, data } = message
    
    if (!this.config.message.allowedTypes.includes(type)) {
      console.warn('不允许的消息类型:', type)
      return
    }
    
    switch (type) {
      case 'navigation':
        this.handleNavigationMessage(data)
        break
      case 'userAction':
        this.handleUserActionMessage(data)
        break
      case 'dataSync':
        this.handleDataSyncMessage(data)
        break
      case 'error':
        this.handleErrorMessage(data)
        break
      case 'ready':
        this.handleReadyMessage(data)
        break
      default:
        console.log('未处理的消息类型:', type, data)
    }
    
    this.emit('webMessage', { type, data })
  }
  
  /**
   * 处理导航消息
   */
  handleNavigationMessage(data) {
    const { action, pageName, params } = data
    
    switch (action) {
      case 'navigateTo':
        this.navigateTo(pageName, { params })
        break
      case 'redirectTo':
        this.navigateTo(pageName, { params, navigationType: 'redirectTo' })
        break
      case 'navigateBack':
        uni.navigateBack({ delta: data.delta || 1 })
        break
      default:
        console.warn('未知的导航动作:', action)
    }
  }
  
  /**
   * 处理用户行为消息
   */
  handleUserActionMessage(data) {
    // 可以在这里处理用户行为统计等
    console.log('用户行为:', data)
  }
  
  /**
   * 处理数据同步消息
   */
  handleDataSyncMessage(data) {
    // 可以在这里处理数据同步
    console.log('数据同步:', data)
  }
  
  /**
   * 处理错误消息
   */
  handleErrorMessage(data) {
    console.error('Web端错误:', data)
    
    if (this.config.error.showErrorToast) {
      uni.showToast({
        title: data.message || '发生错误',
        icon: 'none'
      })
    }
  }
  
  /**
   * 处理就绪消息
   */
  handleReadyMessage(data) {
    console.log('Web端就绪:', data)
    this.emit('webReady', data)
  }
  
  /**
   * 发送消息到web端
   * @param {object} webviewContext web-view组件的上下文
   * @param {object} message 消息对象
   */
  sendMessageToWeb(webviewContext, message) {
    if (webviewContext && typeof webviewContext.postMessage === 'function') {
      webviewContext.postMessage({
        data: message
      })
    }
  }
  
  /**
   * 事件发射器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }
  
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }
  
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调执行错误:', error)
        }
      })
    }
  }
  
  /**
   * 获取当前配置
   */
  getConfig() {
    return this.config
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    Object.assign(this.config, newConfig)
  }
}

// 创建单例实例
const hybridUtils = new HybridUtils()

export default hybridUtils
