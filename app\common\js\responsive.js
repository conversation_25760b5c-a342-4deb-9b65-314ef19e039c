/**
 * 响应式工具类
 * 基于微信官方大屏适配指南实现
 */

class ResponsiveHelper {
  constructor() {
    this.breakpoints = {
      xs: 0,     // 超小屏幕 手机
      sm: 576,   // 小屏幕 大手机
      md: 768,   // 中等屏幕 平板
      lg: 992,   // 大屏幕 桌面
      xl: 1200,  // 超大屏幕 大桌面
      xxl: 1600  // 超超大屏幕
    };
    
    this.currentBreakpoint = 'xs';
    this.systemInfo = null;
    this.isLargeScreen = false;
    this.aspectRatio = 1;
    
    this.init();
  }
  
  init() {
    try {
      this.systemInfo = uni.getSystemInfoSync();
      this.updateBreakpoint();
      this.detectLargeScreen();
    } catch (error) {
      console.error('响应式工具初始化失败:', error);
    }
  }
  
  updateBreakpoint() {
    const width = this.systemInfo.windowWidth;
    
    if (width >= this.breakpoints.xxl) {
      this.currentBreakpoint = 'xxl';
    } else if (width >= this.breakpoints.xl) {
      this.currentBreakpoint = 'xl';
    } else if (width >= this.breakpoints.lg) {
      this.currentBreakpoint = 'lg';
    } else if (width >= this.breakpoints.md) {
      this.currentBreakpoint = 'md';
    } else if (width >= this.breakpoints.sm) {
      this.currentBreakpoint = 'sm';
    } else {
      this.currentBreakpoint = 'xs';
    }
  }
  
  detectLargeScreen() {
    const { windowWidth, windowHeight, platform, system } = this.systemInfo;
    this.aspectRatio = windowWidth / windowHeight;
    
    // 判断是否为大屏设备
    this.isLargeScreen = 
      windowWidth >= this.breakpoints.md || // 宽度大于768
      this.aspectRatio > 1.0 || // 横屏
      platform === 'windows' ||
      platform === 'mac' ||
      (system && (system.includes('Windows') || system.includes('Mac')));
  }
  
  // 获取当前断点
  getCurrentBreakpoint() {
    return this.currentBreakpoint;
  }
  
  // 判断是否为指定断点或更大
  isBreakpointUp(breakpoint) {
    const breakpointValues = Object.keys(this.breakpoints);
    const currentIndex = breakpointValues.indexOf(this.currentBreakpoint);
    const targetIndex = breakpointValues.indexOf(breakpoint);
    return currentIndex >= targetIndex;
  }
  
  // 判断是否为指定断点或更小
  isBreakpointDown(breakpoint) {
    const breakpointValues = Object.keys(this.breakpoints);
    const currentIndex = breakpointValues.indexOf(this.currentBreakpoint);
    const targetIndex = breakpointValues.indexOf(breakpoint);
    return currentIndex <= targetIndex;
  }
  
  // 获取栅格列数
  getGridCols() {
    switch (this.currentBreakpoint) {
      case 'xs': return 1;
      case 'sm': return 2;
      case 'md': return 3;
      case 'lg': return 4;
      case 'xl': return 5;
      case 'xxl': return 6;
      default: return 1;
    }
  }
  
  // 获取容器最大宽度
  getContainerMaxWidth() {
    switch (this.currentBreakpoint) {
      case 'xs': return '100%';
      case 'sm': return '540px';
      case 'md': return '720px';
      case 'lg': return '960px';
      case 'xl': return '1140px';
      case 'xxl': return '1320px';
      default: return '100%';
    }
  }
  
  // 获取响应式字体大小
  getResponsiveFontSize(baseSize = 14) {
    const scale = this.isLargeScreen ? 1.1 : 1;
    return Math.round(baseSize * scale);
  }
  
  // 获取响应式间距
  getResponsiveSpacing(baseSpacing = 10) {
    switch (this.currentBreakpoint) {
      case 'xs': return baseSpacing;
      case 'sm': return baseSpacing * 1.2;
      case 'md': return baseSpacing * 1.4;
      case 'lg': return baseSpacing * 1.6;
      case 'xl': return baseSpacing * 1.8;
      case 'xxl': return baseSpacing * 2;
      default: return baseSpacing;
    }
  }
  
  // 获取布局模式
  getLayoutMode() {
    if (this.isBreakpointUp('lg')) {
      return 'desktop'; // 桌面布局：侧边栏、多列
    } else if (this.isBreakpointUp('md')) {
      return 'tablet'; // 平板布局：两列、横向扩展
    } else {
      return 'mobile'; // 手机布局：单列、垂直堆叠
    }
  }
  
  // 获取列表显示模式
  getListDisplayMode() {
    switch (this.currentBreakpoint) {
      case 'xs':
      case 'sm': return 'list'; // 列表模式
      case 'md': return 'grid-2'; // 2列网格
      case 'lg': return 'grid-3'; // 3列网格
      case 'xl': return 'grid-4'; // 4列网格
      case 'xxl': return 'grid-5'; // 5列网格
      default: return 'list';
    }
  }
  
  // 获取侧边栏宽度
  getSidebarWidth() {
    if (this.isBreakpointUp('lg')) {
      return '240px';
    } else if (this.isBreakpointUp('md')) {
      return '200px';
    } else {
      return '0px';
    }
  }
  
  // 判断是否显示侧边栏
  shouldShowSidebar() {
    return this.isBreakpointUp('lg');
  }
  
  // 获取卡片宽度
  getCardWidth() {
    const containerWidth = this.systemInfo.windowWidth;
    const cols = this.getGridCols();
    const spacing = this.getResponsiveSpacing(10);
    const totalSpacing = spacing * (cols + 1);
    return Math.floor((containerWidth - totalSpacing) / cols);
  }
  
  // 监听窗口大小变化
  onResize(callback) {
    // 注意：小程序中需要在页面的onResize中调用
    this.init();
    if (typeof callback === 'function') {
      callback(this);
    }
  }
  
  // 获取系统信息
  getSystemInfo() {
    return this.systemInfo;
  }
  
  // 获取调试信息
  getDebugInfo() {
    return {
      breakpoint: this.currentBreakpoint,
      isLargeScreen: this.isLargeScreen,
      layoutMode: this.getLayoutMode(),
      gridCols: this.getGridCols(),
      windowWidth: this.systemInfo.windowWidth,
      windowHeight: this.systemInfo.windowHeight,
      aspectRatio: this.aspectRatio.toFixed(2)
    };
  }
}

// 创建全局实例
const responsiveHelper = new ResponsiveHelper();

export default responsiveHelper;
