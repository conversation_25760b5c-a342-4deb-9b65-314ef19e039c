<template>
  <view class="hybrid-container">
    <!-- 大屏模式：显示web端 -->
    <web-view 
      v-if="showWebView" 
      :src="computedWebUrl" 
      @message="handleWebMessage"
      @load="handleWebLoad"
      @error="handleWebError"
      class="web-view">
    </web-view>
    
    <!-- 小屏模式：显示原生内容 -->
    <view v-else class="native-view">
      <slot></slot>
    </view>
    
    <!-- 调试信息（开发环境显示） -->
    <view v-if="showDebugInfo" class="debug-info">
      <text>屏幕: {{screenInfo.windowWidth}}x{{screenInfo.windowHeight}}</text>
      <text>模式: {{showWebView ? 'Web端' : '原生'}}</text>
      <text>比例: {{screenRatio.toFixed(2)}}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'HybridView',
  props: {
    // web端URL
    webUrl: {
      type: String,
      default: ''
    },
    // 强制模式：'web', 'native', 'auto'
    forceMode: {
      type: String,
      default: 'auto'
    },
    // 大屏阈值
    largeScreenWidth: {
      type: Number,
      default: 768
    },
    // 宽高比阈值
    aspectRatioThreshold: {
      type: Number,
      default: 1.2
    },
    // 是否显示调试信息
    debugMode: {
      type: Boolean,
      default: false
    },
    // URL参数
    urlParams: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      screenInfo: {},
      showDebugInfo: false
    }
  },
  
  computed: {
    // 屏幕宽高比
    screenRatio() {
      const { windowWidth = 0, windowHeight = 1 } = this.screenInfo
      return windowWidth / windowHeight
    },
    
    // 是否显示web端
    showWebView() {
      if (this.forceMode === 'web') return true
      if (this.forceMode === 'native') return false
      
      const { windowWidth = 0 } = this.screenInfo
      return windowWidth > this.largeScreenWidth || 
             this.screenRatio > this.aspectRatioThreshold
    },
    
    // 计算后的web URL
    computedWebUrl() {
      if (!this.webUrl) return ''
      
      const url = new URL(this.webUrl)
      
      // 添加小程序标识
      url.searchParams.set('from', 'miniprogram')
      url.searchParams.set('platform', 'wechat')
      
      // 添加屏幕信息
      url.searchParams.set('screenWidth', this.screenInfo.windowWidth)
      url.searchParams.set('screenHeight', this.screenInfo.windowHeight)
      
      // 添加自定义参数
      Object.keys(this.urlParams).forEach(key => {
        url.searchParams.set(key, this.urlParams[key])
      })
      
      return url.toString()
    }
  },
  
  mounted() {
    this.initScreenInfo()
    this.showDebugInfo = this.debugMode
  },
  
  methods: {
    // 初始化屏幕信息
    initScreenInfo() {
      try {
        this.screenInfo = uni.getSystemInfoSync()
        console.log('屏幕信息:', this.screenInfo)
        
        // 触发模式变化事件
        this.$emit('modeChange', {
          mode: this.showWebView ? 'web' : 'native',
          screenInfo: this.screenInfo
        })
      } catch (error) {
        console.error('获取屏幕信息失败:', error)
      }
    },
    
    // 处理web端消息
    handleWebMessage(e) {
      console.log('Web端消息:', e.detail.data)
      this.$emit('webMessage', e.detail.data)
    },
    
    // web端加载完成
    handleWebLoad(e) {
      console.log('Web端加载完成')
      this.$emit('webLoad', e)
    },
    
    // web端加载错误
    handleWebError(e) {
      console.error('Web端加载错误:', e)
      this.$emit('webError', e)
      
      uni.showToast({
        title: '页面加载失败',
        icon: 'none'
      })
    },
    
    // 手动刷新屏幕信息
    refreshScreenInfo() {
      this.initScreenInfo()
    },
    
    // 切换调试模式
    toggleDebugMode() {
      this.showDebugInfo = !this.showDebugInfo
    }
  }
}
</script>

<style scoped>
.hybrid-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.web-view {
  width: 100%;
  height: 100%;
}

.native-view {
  width: 100%;
  height: 100%;
}

.debug-info {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 24rpx;
  z-index: 9999;
}

.debug-info text {
  display: block;
  margin-bottom: 10rpx;
}
</style>
