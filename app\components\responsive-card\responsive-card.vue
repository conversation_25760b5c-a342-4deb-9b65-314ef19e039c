<template>
  <view :class="cardClasses" :style="cardStyle" @tap="handleTap">
    <slot></slot>
  </view>
</template>

<script>
import responsiveMixin from '@/mixins/responsive.js';

export default {
  name: 'ResponsiveCard',
  mixins: [responsiveMixin],
  props: {
    shadow: {
      type: String,
      default: 'normal' // none, light, normal, heavy
    },
    padding: {
      type: [Number, String],
      default: 20
    },
    radius: {
      type: [Number, String],
      default: 8
    },
    hover: {
      type: Boolean,
      default: true
    },
    clickable: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    cardClasses() {
      return [
        'responsive-card',
        this.responsiveClasses,
        `shadow-${this.shadow}`,
        {
          'card-hover': this.hover,
          'card-clickable': this.clickable
        }
      ];
    },
    cardStyle() {
      const padding = this.getResponsiveSpacing(this.padding);
      const radius = typeof this.radius === 'number' ? `${this.radius}rpx` : this.radius;
      
      return {
        padding: `${padding}rpx`,
        borderRadius: radius
      };
    }
  },
  methods: {
    handleTap(e) {
      if (this.clickable) {
        this.$emit('tap', e);
      }
    }
  }
};
</script>

<style scoped>
.responsive-card {
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.shadow-none {
  box-shadow: none;
}

.shadow-light {
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.1);
}

.shadow-normal {
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.shadow-heavy {
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}

.card-hover:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.15);
}

.card-clickable {
  cursor: pointer;
}

.card-clickable:active {
  transform: translateY(1rpx);
}
</style>
