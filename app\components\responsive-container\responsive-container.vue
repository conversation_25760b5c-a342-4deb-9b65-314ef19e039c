<template>
  <view :class="containerClasses" :style="containerStyle">
    <slot></slot>
  </view>
</template>

<script>
import responsiveMixin from '@/mixins/responsive.js';

export default {
  name: 'ResponsiveContainer',
  mixins: [responsiveMixin],
  props: {
    fluid: {
      type: Boolean,
      default: false
    },
    padding: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    containerClasses() {
      return [
        'responsive-container',
        this.responsiveClasses,
        {
          'container-fluid': this.fluid,
          'no-padding': !this.padding
        }
      ];
    },
    containerStyle() {
      if (this.fluid) {
        return {
          maxWidth: '100%',
          padding: this.padding ? `0 ${this.getResponsiveSpacing(15)}rpx` : '0'
        };
      }
      return this.containerStyle;
    }
  }
};
</script>

<style scoped>
.responsive-container {
  width: 100%;
  margin: 0 auto;
}

.container-fluid {
  max-width: 100% !important;
}

.no-padding {
  padding: 0 !important;
}
</style>
