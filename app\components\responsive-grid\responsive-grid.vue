<template>
  <view :class="gridClasses" :style="gridStyle">
    <slot></slot>
  </view>
</template>

<script>
import responsiveMixin from '@/mixins/responsive.js';

export default {
  name: 'ResponsiveGrid',
  mixins: [responsiveMixin],
  props: {
    cols: {
      type: [Number, Object],
      default: () => ({
        xs: 1,
        sm: 2,
        md: 3,
        lg: 4,
        xl: 5,
        xxl: 6
      })
    },
    gap: {
      type: [Number, String],
      default: 10
    },
    align: {
      type: String,
      default: 'stretch'
    },
    justify: {
      type: String,
      default: 'start'
    }
  },
  computed: {
    gridClasses() {
      return [
        'responsive-grid',
        this.responsiveClasses,
        `grid-align-${this.align}`,
        `grid-justify-${this.justify}`
      ];
    },
    gridStyle() {
      const currentCols = this.getCurrentCols();
      const spacing = this.getResponsiveSpacing(this.gap);
      
      return {
        display: 'grid',
        gridTemplateColumns: `repeat(${currentCols}, 1fr)`,
        gap: `${spacing}rpx`,
        alignItems: this.align,
        justifyContent: this.justify
      };
    }
  },
  methods: {
    getCurrentCols() {
      if (typeof this.cols === 'number') {
        return this.cols;
      }
      
      const breakpoint = this.responsive.breakpoint;
      return this.cols[breakpoint] || this.cols.xs || 1;
    }
  }
};
</script>

<style scoped>
.responsive-grid {
  width: 100%;
}

.grid-align-start {
  align-items: flex-start;
}

.grid-align-center {
  align-items: center;
}

.grid-align-end {
  align-items: flex-end;
}

.grid-align-stretch {
  align-items: stretch;
}

.grid-justify-start {
  justify-content: flex-start;
}

.grid-justify-center {
  justify-content: center;
}

.grid-justify-end {
  justify-content: flex-end;
}

.grid-justify-between {
  justify-content: space-between;
}

.grid-justify-around {
  justify-content: space-around;
}
</style>
