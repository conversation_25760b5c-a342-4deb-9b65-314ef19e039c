# 小程序混合模式使用指南

## 概述

本方案解决了微信小程序在PC端大屏模式下的响应式布局问题，通过自动检测屏幕尺寸，在大屏时显示Web端界面，小屏时显示原生小程序界面。

## 核心特性

- 🔄 **自动切换**: 根据屏幕尺寸自动选择显示模式
- 🌐 **Web端集成**: 在大屏模式下无缝嵌入Web端界面
- 📱 **原生体验**: 小屏模式下保持原生小程序体验
- ⚙️ **灵活配置**: 支持强制模式和自定义阈值
- 💬 **消息通信**: Web端与小程序之间的双向通信
- 🛠️ **开发友好**: 提供调试模式和演示页面

## 文件结构

```
app/
├── components/
│   └── hybrid-view/           # 混合视图组件
│       └── hybrid-view.vue
├── pages/
│   ├── webview/              # Web端显示页面
│   │   └── webview.vue
│   └── hybrid-demo/          # 演示页面
│       └── hybrid-demo.vue
├── common/
│   ├── config/
│   │   └── hybrid-config.js  # 混合模式配置
│   └── js/
│       └── hybrid-utils.js   # 混合模式工具类
└── docs/
    └── hybrid-mode-guide.md  # 使用指南
```

## 快速开始

### 1. 配置Web端URL

编辑 `app/common/config/hybrid-config.js`：

```javascript
export default {
  web: {
    productionUrl: 'https://your-production-domain.com',
    developmentUrl: 'https://your-dev-domain.com',
    localUrl: 'http://localhost:8080'
  }
  // ... 其他配置
}
```

### 2. 使用混合视图组件

在页面中使用 `hybrid-view` 组件：

```vue
<template>
  <hybrid-view 
    :web-url="webUrl"
    :force-mode="forceMode"
    @modeChange="handleModeChange">
    
    <!-- 原生小程序内容 -->
    <view class="native-content">
      <!-- 你的原生界面内容 -->
    </view>
  </hybrid-view>
</template>

<script>
import HybridView from '@/components/hybrid-view/hybrid-view.vue'

export default {
  components: { HybridView },
  data() {
    return {
      webUrl: 'https://your-web-domain.com',
      forceMode: 'auto' // 'auto', 'web', 'native'
    }
  },
  methods: {
    handleModeChange(data) {
      console.log('模式切换:', data.mode)
    }
  }
}
</script>
```

### 3. 使用工具类导航

使用 `hybridUtils` 进行页面导航：

```javascript
import hybridUtils from '@/common/js/hybrid-utils.js'

// 自动选择模式导航
hybridUtils.navigateTo('index', {
  params: { id: 123 }
})

// 强制使用Web端
hybridUtils.navigateTo('search', {
  forceMode: 'web',
  params: { keyword: 'test' }
})
```

## 配置说明

### 屏幕检测配置

```javascript
screen: {
  largeScreenWidth: 768,        // 大屏宽度阈值（像素）
  aspectRatioThreshold: 1.2,    // 宽高比阈值
  detectionDelay: 100           // 检测延迟（毫秒）
}
```

### 页面配置

```javascript
pages: {
  index: {
    webPath: '/',                    // Web端路径
    nativePath: '/pages/index/index', // 原生页面路径
    title: '首页',
    enableHybrid: true               // 是否启用混合模式
  }
}
```

### 消息通信配置

```javascript
message: {
  allowedTypes: [                    // 允许的消息类型
    'navigation',
    'userAction', 
    'dataSync',
    'error',
    'ready'
  ],
  timeout: 5000                      // 消息超时时间
}
```

## Web端集成

### 1. Web端消息发送

在Web端向小程序发送消息：

```javascript
// Web端代码
window.wx.miniProgram.postMessage({
  data: {
    type: 'navigation',
    data: {
      action: 'navigateTo',
      pageName: 'search',
      params: { keyword: 'test' }
    }
  }
})
```

### 2. 小程序消息处理

小程序自动处理Web端消息：

```javascript
// 在 hybrid-utils.js 中已实现
handleWebMessage(message) {
  const { type, data } = message
  switch (type) {
    case 'navigation':
      this.handleNavigationMessage(data)
      break
    // ... 其他消息类型
  }
}
```

## 调试模式

### 启用调试模式

```vue
<hybrid-view 
  :debug-mode="true"
  :web-url="webUrl">
  <!-- 内容 -->
</hybrid-view>
```

调试模式会显示：
- 当前屏幕尺寸
- 当前显示模式
- 屏幕宽高比

### 演示页面

访问 `/pages/hybrid-demo/hybrid-demo` 查看完整演示。

## 最佳实践

### 1. 页面设计

- **一致性**: 确保Web端和原生端的用户体验一致
- **响应式**: Web端需要适配不同屏幕尺寸
- **性能**: 优化Web端加载速度

### 2. 消息通信

- **类型安全**: 使用预定义的消息类型
- **错误处理**: 实现消息超时和错误处理
- **数据同步**: 保持Web端和原生端数据一致

### 3. 配置管理

- **环境区分**: 为不同环境配置不同的Web端URL
- **阈值调整**: 根据实际需求调整屏幕检测阈值
- **功能开关**: 为不同页面配置是否启用混合模式

## 常见问题

### Q: 如何处理Web端加载失败？

A: 系统会自动回退到原生模式，可以在配置中设置错误处理策略：

```javascript
error: {
  fallbackStrategy: 'native',  // 回退到原生模式
  showErrorToast: true,        // 显示错误提示
  maxRetries: 3               // 最大重试次数
}
```

### Q: 如何自定义屏幕检测逻辑？

A: 可以重写 `shouldUseWeb` 方法：

```javascript
// 在 hybrid-config.js 中
shouldUseWeb(screenInfo, pageName, forceMode) {
  // 自定义检测逻辑
  return customLogic(screenInfo)
}
```

### Q: 如何在Web端获取小程序信息？

A: 通过URL参数传递：

```javascript
// 小程序会自动添加这些参数
const url = new URL(webUrl)
url.searchParams.set('from', 'miniprogram')
url.searchParams.set('screenWidth', screenWidth)
// ... 其他参数
```

## 注意事项

1. **域名配置**: 确保Web端域名已在小程序后台配置业务域名
2. **HTTPS要求**: Web端必须使用HTTPS协议
3. **功能限制**: Web端无法使用小程序特有的API
4. **性能考虑**: 大屏模式下Web端加载可能影响首屏时间
5. **兼容性**: 确保Web端在不同浏览器内核下的兼容性

## 更新日志

- v1.0.0: 初始版本，支持基本的混合模式功能
- 后续版本将添加更多功能和优化
