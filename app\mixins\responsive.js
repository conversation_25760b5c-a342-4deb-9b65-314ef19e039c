/**
 * 响应式Mixin
 * 为页面提供响应式能力
 */
import responsiveHelper from '@/common/js/responsive.js';

export default {
  data() {
    return {
      // 响应式状态
      responsive: {
        breakpoint: 'xs',
        isLargeScreen: false,
        layoutMode: 'mobile',
        gridCols: 1,
        showSidebar: false,
        listDisplayMode: 'list'
      }
    };
  },
  
  computed: {
    // 响应式类名
    responsiveClasses() {
      return [
        `breakpoint-${this.responsive.breakpoint}`,
        `layout-${this.responsive.layoutMode}`,
        `grid-cols-${this.responsive.gridCols}`,
        this.responsive.isLargeScreen ? 'large-screen' : 'small-screen',
        this.responsive.showSidebar ? 'with-sidebar' : 'no-sidebar'
      ].join(' ');
    },
    
    // 容器样式
    containerStyle() {
      return {
        maxWidth: responsiveHelper.getContainerMaxWidth(),
        margin: '0 auto',
        padding: `0 ${responsiveHelper.getResponsiveSpacing(15)}rpx`
      };
    },
    
    // 栅格样式
    gridStyle() {
      const cols = this.responsive.gridCols;
      const spacing = responsiveHelper.getResponsiveSpacing(10);
      return {
        display: 'grid',
        gridTemplateColumns: `repeat(${cols}, 1fr)`,
        gap: `${spacing}rpx`,
        padding: `${spacing}rpx`
      };
    },
    
    // 侧边栏样式
    sidebarStyle() {
      return {
        width: responsiveHelper.getSidebarWidth(),
        display: this.responsive.showSidebar ? 'block' : 'none'
      };
    }
  },
  
  onLoad() {
    this.initResponsive();
  },
  
  onShow() {
    this.updateResponsive();
  },
  
  onResize() {
    this.updateResponsive();
  },
  
  methods: {
    // 初始化响应式
    initResponsive() {
      this.updateResponsive();
    },
    
    // 更新响应式状态
    updateResponsive() {
      responsiveHelper.onResize(() => {
        this.responsive = {
          breakpoint: responsiveHelper.getCurrentBreakpoint(),
          isLargeScreen: responsiveHelper.isLargeScreen,
          layoutMode: responsiveHelper.getLayoutMode(),
          gridCols: responsiveHelper.getGridCols(),
          showSidebar: responsiveHelper.shouldShowSidebar(),
          listDisplayMode: responsiveHelper.getListDisplayMode()
        };
      });
    },
    
    // 获取响应式值
    getResponsiveValue(values) {
      const breakpoint = this.responsive.breakpoint;
      if (typeof values === 'object' && values[breakpoint] !== undefined) {
        return values[breakpoint];
      }
      return values;
    },
    
    // 获取响应式字体大小
    getResponsiveFontSize(baseSize) {
      return responsiveHelper.getResponsiveFontSize(baseSize);
    },
    
    // 获取响应式间距
    getResponsiveSpacing(baseSpacing) {
      return responsiveHelper.getResponsiveSpacing(baseSpacing);
    },
    
    // 判断断点
    isBreakpointUp(breakpoint) {
      return responsiveHelper.isBreakpointUp(breakpoint);
    },
    
    isBreakpointDown(breakpoint) {
      return responsiveHelper.isBreakpointDown(breakpoint);
    },
    
    // 获取卡片宽度
    getCardWidth() {
      return responsiveHelper.getCardWidth();
    },
    
    // 获取列表项样式
    getListItemStyle() {
      const mode = this.responsive.listDisplayMode;
      if (mode.startsWith('grid-')) {
        const cols = parseInt(mode.split('-')[1]);
        const spacing = this.getResponsiveSpacing(10);
        return {
          width: `calc(${100/cols}% - ${spacing}rpx)`,
          marginBottom: `${spacing}rpx`
        };
      }
      return {
        width: '100%',
        marginBottom: `${this.getResponsiveSpacing(10)}rpx`
      };
    },
    
    // 获取调试信息
    getResponsiveDebugInfo() {
      return responsiveHelper.getDebugInfo();
    }
  }
};
