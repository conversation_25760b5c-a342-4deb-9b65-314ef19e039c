<template>
  <view class="demo-container">
    <back :showBackText="false" :showBackIcon="true" :showBackLeft="true" :showHomeIcon="false"
      customClass="bg-gradual-blue text-white" title="混合模式演示"></back>
    
    <view class="demo-content">
      <!-- 模式选择 -->
      <view class="mode-selector">
        <view class="selector-title">选择显示模式</view>
        <view class="selector-buttons">
          <button 
            class="cu-btn margin-sm"
            :class="currentMode === 'auto' ? 'bg-blue' : 'line-blue'"
            @tap="switchMode('auto')">
            自动模式
          </button>
          <button 
            class="cu-btn margin-sm"
            :class="currentMode === 'web' ? 'bg-green' : 'line-green'"
            @tap="switchMode('web')">
            Web端
          </button>
          <button 
            class="cu-btn margin-sm"
            :class="currentMode === 'native' ? 'bg-orange' : 'line-orange'"
            @tap="switchMode('native')">
            原生端
          </button>
        </view>
      </view>
      
      <!-- 页面选择 -->
      <view class="page-selector">
        <view class="selector-title">选择要演示的页面</view>
        <view class="page-list">
          <view 
            v-for="(page, key) in availablePages" 
            :key="key"
            class="page-item"
            @tap="openPage(key)">
            <view class="page-info">
              <text class="page-name">{{ page.title }}</text>
              <text class="page-desc">{{ page.enableHybrid ? '支持混合模式' : '仅原生模式' }}</text>
            </view>
            <text class="cuIcon-right text-gray"></text>
          </view>
        </view>
      </view>
      
      <!-- 当前状态 -->
      <view class="status-panel">
        <view class="status-title">当前状态</view>
        <view class="status-info">
          <view class="status-item">
            <text class="status-label">屏幕尺寸:</text>
            <text class="status-value">{{ screenInfo.windowWidth }}x{{ screenInfo.windowHeight }}</text>
          </view>
          <view class="status-item">
            <text class="status-label">宽高比:</text>
            <text class="status-value">{{ aspectRatio }}</text>
          </view>
          <view class="status-item">
            <text class="status-label">推荐模式:</text>
            <text class="status-value">{{ recommendedMode }}</text>
          </view>
          <view class="status-item">
            <text class="status-label">当前模式:</text>
            <text class="status-value">{{ currentMode }}</text>
          </view>
        </view>
      </view>
      
      <!-- 配置信息 -->
      <view class="config-panel">
        <view class="config-title">配置信息</view>
        <view class="config-info">
          <view class="config-item">
            <text class="config-label">大屏阈值:</text>
            <text class="config-value">{{ config.screen.largeScreenWidth }}px</text>
          </view>
          <view class="config-item">
            <text class="config-label">宽高比阈值:</text>
            <text class="config-value">{{ config.screen.aspectRatioThreshold }}</text>
          </view>
          <view class="config-item">
            <text class="config-label">Web端URL:</text>
            <text class="config-value">{{ config.web.currentUrl }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import hybridUtils from '@/common/js/hybrid-utils.js'

export default {
  data() {
    return {
      currentMode: 'auto',
      screenInfo: {},
      config: {},
      availablePages: {}
    }
  },
  
  computed: {
    aspectRatio() {
      const { windowWidth = 0, windowHeight = 1 } = this.screenInfo
      return (windowWidth / windowHeight).toFixed(2)
    },
    
    recommendedMode() {
      if (!this.screenInfo.windowWidth) return '未知'
      
      const shouldUseWeb = hybridUtils.shouldUseWeb('index', 'auto')
      return shouldUseWeb ? 'Web端' : '原生端'
    }
  },
  
  onLoad() {
    this.initData()
  },
  
  onShow() {
    this.updateScreenInfo()
  },
  
  methods: {
    // 初始化数据
    initData() {
      hybridUtils.init()
      this.config = hybridUtils.getConfig()
      this.availablePages = this.config.pages
      this.updateScreenInfo()
    },
    
    // 更新屏幕信息
    updateScreenInfo() {
      hybridUtils.updateScreenInfo()
      this.screenInfo = hybridUtils.screenInfo || {}
    },
    
    // 切换模式
    switchMode(mode) {
      this.currentMode = mode
      uni.showToast({
        title: `切换到${this.getModeText(mode)}`,
        icon: 'none'
      })
    },
    
    // 获取模式文本
    getModeText(mode) {
      switch(mode) {
        case 'web': return 'Web端模式'
        case 'native': return '原生模式'
        default: return '自动模式'
      }
    },
    
    // 打开页面
    openPage(pageName) {
      const pageConfig = this.availablePages[pageName]
      if (!pageConfig) {
        uni.showToast({
          title: '页面配置不存在',
          icon: 'none'
        })
        return
      }
      
      if (!pageConfig.enableHybrid && this.currentMode === 'web') {
        uni.showModal({
          title: '提示',
          content: '该页面不支持Web端模式，将使用原生模式打开',
          success: (res) => {
            if (res.confirm) {
              this.navigateToPage(pageName, 'native')
            }
          }
        })
        return
      }
      
      this.navigateToPage(pageName, this.currentMode)
    },
    
    // 导航到页面
    navigateToPage(pageName, mode) {
      hybridUtils.navigateTo(pageName, {
        forceMode: mode,
        params: {
          demo: true,
          timestamp: Date.now()
        }
      })
    }
  }
}
</script>

<style scoped>
.demo-container {
  background: #f8f9fa;
  min-height: 100vh;
}

.demo-content {
  padding: 20rpx;
}

.mode-selector,
.page-selector,
.status-panel,
.config-panel {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.selector-title,
.status-title,
.config-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.selector-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.page-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.page-info {
  flex: 1;
}

.page-name {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.page-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.status-info,
.config-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.status-item,
.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label,
.config-label {
  font-size: 28rpx;
  color: #666;
}

.status-value,
.config-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
</style>
