import responsiveMixin from '@/mixins/responsive.js';

let that = null,
	app = getApp(),
	config = app.globalData.config,
	cache = config.storage,
	helper = app.globalData.helper;

export default {
	mixins: [responsiveMixin],
	data() {
		return {
			images: [],
			appIsAudit: false,
			showWxGroup: false,
			showWxGroupText: '',
			appPlatform: app.globalData.appPlatform,
			showIndexShareName: '',
			articleList: [],
			currentCity: {},
			currentExam: {},
			currentProfession: {},
			menuList: [],
			examTime: {},
			isLoading: true
		};
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;
	},
	onShow(options) {
		that.getHomeData();
		that.initIndexExamData();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 通用导航方法
		 * @param {String} url 完整的导航地址
		 */
		goTo(url) {
			uni.navigateTo({url});
		},
		
		checkAppIsAudit() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		initIndexExamData() {
			that.currentCity = cache.getCurrentCityData();
			that.currentExam = cache.getCurrentExamData();
			that.currentProfession = cache.getCurrentProfessionData();
			that.changeExamInfo();
		},
		async changeExamInfo() {
			let exam_id = helper.variableDefalut(that.currentExam.id, 0),
				region_id = helper.variableDefalut(that.currentCity.id, 0),
				profession_id = helper.variableDefalut(that.currentProfession.id, 0);
			if (region_id == 0) {
				that.goTo('city/city?first_visit=1');
				return;
			}
			if (profession_id && app.globalData.isLogin) {
				let user = cache.getUserInfoData(),
					user_profession_id = helper
					.variableDefalut(user.profession_id, 0);
				if (user_profession_id != profession_id) {
					await app.globalData.service.userUpdate({
						exam_id: exam_id,
						region_id: region_id,
						profession_id: profession_id
					});
					user.profession_id = profession_id;
					cache.setUserInfoData(user);
				}
			}
		},
		
		/**
		 * 获取首页数据
		 */
		getHomeData() {
			that.isLoading = true;
			// 优先加载缓存
			let time = app.globalData.getTimestamp();
			let cacheHomeData = wx.getStorageSync(cache.homeKey)
			console.log(cacheHomeData, time);
			if (cacheHomeData && cacheHomeData.expire >= time) {
				that.images = cacheHomeData.swiper;
				that.menuList = cacheHomeData.menuList
				that.articleList = cacheHomeData.articleList;
				that.showWxGroup = cacheHomeData.appConfig.showWxGroup
				that.showWxGroupText = cacheHomeData.appConfig.showWxGroupText
				that.showIndexShareName = cacheHomeData.appConfig.showIndexShareName
				// 从缓存中获取考试时间数据
				if (cacheHomeData.examTime) {
					that.examTime = cacheHomeData.examTime;
				}
				that.isLoading = false;
				return;
			}

			// 加载接口数据
			let cacheTime = 0;
			app.globalData.server
				.getRequest('home', {})
				.then((res) => {
					that.images = res.data.swiper;
					that.menuList = res.data.menuList;
					that.articleList = res.data.articleList;
					that.showWxGroup = res.data.appConfig.showWxGroup;
					that.showWxGroupText = res.data.appConfig.showWxGroupText;
					that.showIndexShareName = res.data.appConfig.showIndexShareName;
					
					// 获取考试时间数据
					if (res.data.examTime) {
						that.examTime = res.data.examTime;
					}
					
					res.data.expire = time + cacheTime;
					cache.setHomeData(res.data);
					that.checkAppIsAudit();
					that.isLoading = false;
				})
				.catch((e) => {
					console.log(e)
					app.showToast('获取首页数据失败');
					that.isLoading = false;
				});
		}
	}
};