let that = null,
	app = getApp(),
	config = app.globalData.config,
	cache = config.storage,
	helper = app.globalData.helper;
export default {
	data() {
		return {
			images: [],
			appIsAudit: false,
			showWxGroup: false,
			showWxGroupText: '',
			appPlatform: app.globalData.appPlatform,
			showIndexShareName: '',
			articleList: [],
			currentCity: {},
			currentExam: {},
			currentProfession: {},
			menuList: [],
			examTime: {},
			isLoading: true,
			// 混合模式相关
			showWebView: false,
			webUrl: ''
		};
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;

		// 强制测试Web端模式 - 临时测试用
		if (options.forceWeb === 'true') {
			this.showWebView = true;
			this.webUrl = this.buildWebUrl();
			console.log('强制Web端模式:', this.webUrl);
			return;
		}

		this.checkScreenSize();
	},
	onShow(options) {
		that.getHomeData();
		that.initIndexExamData();
		this.checkScreenSize();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 通用导航方法
		 * @param {String} url 完整的导航地址
		 */
		goTo(url) {
			uni.navigateTo({url});
		},
		
		checkAppIsAudit() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		initIndexExamData() {
			that.currentCity = cache.getCurrentCityData();
			that.currentExam = cache.getCurrentExamData();
			that.currentProfession = cache.getCurrentProfessionData();
			that.changeExamInfo();
		},
		async changeExamInfo() {
			let exam_id = helper.variableDefalut(that.currentExam.id, 0),
				region_id = helper.variableDefalut(that.currentCity.id, 0),
				profession_id = helper.variableDefalut(that.currentProfession.id, 0);
			if (region_id == 0) {
				that.goTo('city/city?first_visit=1');
				return;
			}
			if (profession_id && app.globalData.isLogin) {
				let user = cache.getUserInfoData(),
					user_profession_id = helper
					.variableDefalut(user.profession_id, 0);
				if (user_profession_id != profession_id) {
					await app.globalData.service.userUpdate({
						exam_id: exam_id,
						region_id: region_id,
						profession_id: profession_id
					});
					user.profession_id = profession_id;
					cache.setUserInfoData(user);
				}
			}
		},
		
		/**
		 * 获取首页数据
		 */
		getHomeData() {
			that.isLoading = true;
			// 优先加载缓存
			let time = app.globalData.getTimestamp();
			let cacheHomeData = wx.getStorageSync(cache.homeKey)
			console.log(cacheHomeData, time);
			if (cacheHomeData && cacheHomeData.expire >= time) {
				that.images = cacheHomeData.swiper;
				that.menuList = cacheHomeData.menuList
				that.articleList = cacheHomeData.articleList;
				that.showWxGroup = cacheHomeData.appConfig.showWxGroup
				that.showWxGroupText = cacheHomeData.appConfig.showWxGroupText
				that.showIndexShareName = cacheHomeData.appConfig.showIndexShareName
				// 从缓存中获取考试时间数据
				if (cacheHomeData.examTime) {
					that.examTime = cacheHomeData.examTime;
				}
				that.isLoading = false;
				return;
			}

			// 加载接口数据
			let cacheTime = 0;
			app.globalData.server
				.getRequest('home', {})
				.then((res) => {
					that.images = res.data.swiper;
					that.menuList = res.data.menuList;
					that.articleList = res.data.articleList;
					that.showWxGroup = res.data.appConfig.showWxGroup;
					that.showWxGroupText = res.data.appConfig.showWxGroupText;
					that.showIndexShareName = res.data.appConfig.showIndexShareName;
					
					// 获取考试时间数据
					if (res.data.examTime) {
						that.examTime = res.data.examTime;
					}
					
					res.data.expire = time + cacheTime;
					cache.setHomeData(res.data);
					that.checkAppIsAudit();
					that.isLoading = false;
				})
				.catch((e) => {
					console.log(e)
					app.showToast('获取首页数据失败');
					that.isLoading = false;
				});
		},
		// 检测屏幕尺寸
		checkScreenSize() {
			try {
				const systemInfo = uni.getSystemInfoSync();
				const { windowWidth, windowHeight, platform, system } = systemInfo;
				const aspectRatio = windowWidth / windowHeight;

				// 多种方式检测PC端
				const isPC = platform === 'windows' || platform === 'mac' ||
							system.includes('Windows') || system.includes('Mac') ||
							windowWidth > 1000; // 宽度超过1000基本是PC端

				// 大屏判断 - 降低阈值
				const isLargeScreen = windowWidth > 500 || aspectRatio > 0.8;

				// 微信PC端特征检测
				const isWeChatPC = windowWidth > 800 && aspectRatio > 1.0;

				console.log('屏幕信息:', {
					windowWidth,
					windowHeight,
					platform,
					system,
					aspectRatio,
					isPC,
					isLargeScreen,
					isWeChatPC,
					shouldShowWeb: isPC || isLargeScreen || isWeChatPC
				});

				// PC端、大屏或微信PC端都显示Web端
				if (isPC || isLargeScreen || isWeChatPC) {
					this.showWebView = true;
					this.webUrl = this.buildWebUrl();
					console.log('切换到Web端:', this.webUrl);
				} else {
					this.showWebView = false;
					console.log('显示原生内容');
				}
			} catch (error) {
				console.error('获取屏幕信息失败:', error);
				// 出错时也尝试显示Web端
				this.showWebView = true;
				this.webUrl = this.buildWebUrl();
			}
		},
		// 构建Web端URL
		buildWebUrl() {
			const baseUrl = 'https://www.beikeshuati.com';
			const url = new URL('/', baseUrl);

			// 添加小程序标识
			url.searchParams.set('from', 'miniprogram');
			url.searchParams.set('platform', 'wechat');

			// 添加当前选择的城市、考试、专业信息
			if (this.currentCity && this.currentCity.id) {
				url.searchParams.set('city', this.currentCity.id);
			}
			if (this.currentExam && this.currentExam.id) {
				url.searchParams.set('exam', this.currentExam.id);
			}
			if (this.currentProfession && this.currentProfession.id) {
				url.searchParams.set('profession', this.currentProfession.id);
			}

			return url.toString();
		},
		// 处理Web端消息
		handleWebMessage(e) {
			console.log('收到Web端消息:', e.detail.data);
		},
		// Web端加载完成
		handleWebLoad() {
			console.log('Web端加载完成');
		},
		// Web端加载错误
		handleWebError(e) {
			console.error('Web端加载错误:', e);
			// 加载失败时切换到原生模式
			this.showWebView = false;
			uni.showToast({
				title: '页面加载失败，已切换到原生模式',
				icon: 'none'
			});
		}
	}
};