<template>
	<view :class="responsiveClasses">
		<back :showBackText="false" :showBackIcon="false" :showBackLeft="false" :showHomeIcon="false"
			customClass="bg-gradual-blue text-white" title="首页"></back>

		<!-- 响应式主容器 -->
		<view class="responsive-container" v-show="!isLoading">
			<!-- 侧边栏布局（大屏显示） -->
			<view class="responsive-flex" v-if="responsive.showSidebar">
				<!-- 侧边栏 -->
				<view class="responsive-sidebar responsive-padding-base">
					<view class="sidebar-section">
						<text class="responsive-text-lg text-bold text-blue">快速导航</text>
						<view class="sidebar-menu">
							<view class="sidebar-menu-item" @tap="goTo('city/city?first_visit=1')">
								<text class="cuIcon-location text-blue"></text>
								<text class="responsive-text-base">{{ currentCity.name || '选择城市' }}</text>
							</view>
							<view class="sidebar-menu-item" @tap="goTo('exam/exam')">
								<text class="cuIcon-group text-blue"></text>
								<text class="responsive-text-base">{{ currentExam.name || '选择考试' }}</text>
							</view>
							<view class="sidebar-menu-item" @tap="goTo('profession/profession')">
								<text class="cuIcon-read text-blue"></text>
								<text class="responsive-text-base">{{ currentProfession.name || '选择专业' }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 主内容区 -->
				<view class="responsive-main responsive-padding-base">
					<!-- 大屏内容布局 -->
					<view class="desktop-layout">
						<!-- 轮播图 -->
						<view class="swiper-container">
							<swiper class="responsive-swiper" :indicator-dots="true" :circular="true"
								:autoplay="true" interval="5000" duration="500">
								<swiper-item @tap="goTo(item.target_url)" v-for="(item, index) in images" :key="index">
									<image :src="item.img_url" class="responsive-swiper-image" mode="aspectFill"></image>
								</swiper-item>
							</swiper>
						</view>

						<!-- 功能菜单网格 -->
						<view class="menu-grid-container responsive-margin-base">
							<view class="responsive-grid">
								<view class="responsive-grid-item" v-for="(item, index) in menuList" :key="index">
									<view class="menu-card responsive-card" @tap="goTo(item.page_uri)">
										<image class="menu-icon" :src="item.icon" mode="aspectFit"></image>
										<text class="menu-text responsive-text-base">{{item.name}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 移动端布局 -->
			<view v-else class="mobile-layout">
				<!-- 顶部搜索栏 -->
				<view class="cu-bar bg-white search">
					<view class="action" @tap="goTo('city/city?first_visit=1')" style="font-size: 27rpx; max-width: 33.3333%">
						<text class="cuIcon-location text-blue" style="margin: 0"></text>
						<text class="text-blue text-cut">{{ currentCity.name }}</text>
					</view>
					<view class="action" @tap="goTo('exam/exam')" style="font-size: 27rpx; max-width: 33.3333%">
						<text class="cuIcon-group text-blue" style="margin-right: 6rpx"></text>
						<text class="text-blue text-cut">{{ currentExam.name }}</text>
					</view>
					<view class="action" @tap="goTo('profession/profession')" style="font-size: 27rpx; max-width: 33.3333%">
						<text class="cuIcon-read text-blue" style="margin-right: 6rpx"></text>
						<text class="text-blue text-cut">{{ currentProfession.name || '选择专业' }}</text>
					</view>
				</view>

				<!-- 轮播图 -->
				<swiper class="swiper screen-swiper square-dot margin-top-xs" :indicator-dots="true" :circular="true"
					style="min-height: 0;" :autoplay="true" interval="5000" duration="500">
					<swiper-item @tap="goTo(item.target_url)" v-for="(item, index) in images" :key="index">
						<image :src="item.img_url" class="swiper-image"></image>
					</swiper-item>
				</swiper>

			<!-- 分享提示（移动端） -->
			<view v-if="!appIsAudit && showIndexShareName && (appPlatform == 20 || appPlatform == 21)"
				class="cu-bar bg-white margin-top-xs no-border" style="font-size: 50rpx;display:none">
				<view class="action" style="font-size: 48rpx;">
					<text class="cuIcon-notificationfill text-blue" style="font-size: 50rpx;"></text>
					<text class="text-blue text-df">{{ showIndexShareName }}</text>
				</view>
				<view class="action">
					<button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享
						<text class="cuIcon-share" style="margin-left: 6rpx;"></text>
					</button>
				</view>
			</view>

			<!-- 考试倒计时提示 -->
			<view v-if="examTime.show" class="exam-countdown responsive-margin-sm">
				<view class="countdown-content responsive-card responsive-padding-base">
					<view class="countdown-header">
						<text class="cuIcon-time text-blue"></text>
						<text class="countdown-title responsive-text-lg">考试倒计时</text>
					</view>
					<view class="countdown-info">
						<view class="countdown-date responsive-text-base">考试日期：{{ examTime.exam_date }}</view>
						<view class="countdown-timer">
							<text class="countdown-unit responsive-text-sm">还剩</text>
							<text class="countdown-days responsive-text-xl text-bold">{{ examTime.exam_remain }}</text>
							<text class="countdown-unit responsive-text-sm">天</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 学习中心标题 -->
			<view class="cu-bar bg-white margin-top-xs">
				<view class="action sub-title">
					<text class="text-xl text-bold text-blue text-shadow">学习中心</text>
					<text class="text-ABC text-blue">Study</text>
				</view>
				<view class="action">
					<button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享
						<text class="cuIcon-share" style="margin-left: 6rpx;"></text>
					</button>
				</view>
			</view>

			<!-- 响应式功能菜单 -->
			<view class="responsive-list bg-white radius shadow-warp responsive-padding-base">
				<view class="responsive-list-item" v-for="(item, index) in menuList" :key="index">
					<view class="menu-card responsive-card" @tap="goTo(item.page_uri)">
						<image :src="item.icon" mode="aspectFit" class="menu-icon"></image>
						<text class="menu-text responsive-text-base">{{ item.name }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 文章资讯区域 -->
		<view class="article-section" v-if="!appIsAudit">
			<view class="cu-bar bg-white margin-top-xs" style="margin-bottom: 3rpx;">
				<view class="action sub-title">
					<text class="text-xl text-bold text-blue text-shadow">最新资讯</text>
					<text class="text-ABC text-blue">News</text>
				</view>
				<view class="action" @tap="goTo('./article/list')">
					<button class="cu-btn bg-blue margin-tb-sm shadow" data-target="Modal">更多
						<text class="cuIcon-moreandroid" style="margin-left: 5rpx;"></text>
					</button>
				</view>
			</view>

			<!-- 响应式文章列表 -->
			<view class="responsive-list">
				<view class="responsive-list-item"
					v-for="(item, index) in articleList" :key="index"
					v-if="!appIsAudit || (appPlatform != 20 && appPlatform != 21)">
					<view class="article-card responsive-card" @tap="goTo(`./article/detail?id=${item.id}`)">
						<view class="article-content">
							<view class="article-image">
								<image :src="item.thumb" mode="aspectFit" class="radius"></image>
							</view>
							<view class="article-info">
								<view class="article-title responsive-text-base text-black">{{ item.title }}</view>
								<view class="article-meta">
									<view class="text-gray responsive-text-sm">{{ item.cate_name }} · {{ item.create_date }}</view>
									<view class="text-gray responsive-text-sm">{{ item.page_view }} 阅读</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<style src="./index.css"></style>
<script src="./index.js"></script>