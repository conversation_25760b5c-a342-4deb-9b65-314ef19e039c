<template>
	<view>
		<!-- 大屏模式显示Web端 -->
		<web-view
			v-if="showWebView"
			:src="webUrl"
			@message="handleWebMessage"
			@load="handleWebLoad"
			@error="handleWebError">
		</web-view>

		<!-- 小屏模式显示原生内容 -->
		<view v-else class="app-container" :class="{'dark-mode': isDarkMode}">
			<back @beforeBack="handleBeforeBack" :showBackText="false" :customClass="isDarkMode ? 'bg-dark text-white' : 'bg-gradual-blue text-white'" :title="title"></back>
		<view>
			<view class="nav text-center top">
				<view class="cu-item" :class="index==selectModelIndex?'text-blue cur':''"
					v-for="(item,index) in selectModel" :key="index" @tap="selectModelTap" :data-id="index">
					{{item}}
				</view>
			</view>
			<swiper @transition="transitionTap" @animationfinish="animationfinishTap" :current="swiperCurrent"
				:style="'height:calc(100vh - ' + CustomBar + 'px - 120px)'">
				<swiper-item :skip-hidden-item-layout="true" v-for="(item, index) in listData" :key="index">
					<scroll-view scrollY="true" :style="'height:calc(100vh - ' + CustomBar + 'px - 120px)'">

						<view :style="cssData.questionTypeTagAndTitleCss">
							<!--题目类型和题目序号-->
							<view class="questionAsk-layout  margin-top-xs" :style="cssData.questionTypeTagAndTitleCss">
								<view class="cu-capsule radius margin">
									<view class='cu-tag' :style="cssData.questionTypeTagTypeCss">
										{{ item.type_name}}
									</view>
									<view class="cu-tag line-blue" :style="cssData.questionTypeTagCss">
										共{{questionCount}}道题，第{{(currentPage-1) * limit + swiperCurrent + 1}}道题
									</view>
								</view>
							</view>
							<view v-if="backgroundData[item.background_id]">
								<view class="questionAsk-layout margin-top-xs">
									<view class="margin-top margin-left margin-right margin-bottom-xs"
										:style="cssData.questionContentCss">
										<rich-text :nodes="backgroundData[item.background_id]">
										</rich-text>
									</view>
								</view>
							</view>

							<!--题目标题-->
							<view class="questionAsk-layout question-content"
								style="padding:0rpx 30rpx 30rpx 30rpx;">
								<view :class="backgroundData[item.background_id] ? 'margin-top':''">
									<rich-text @longpress="onLongPress(item.questionAsk)" :nodes="item.questionAsk" :style="cssData.questionContentCss">
									</rich-text>
								</view>
								<view class="nosupport" v-if="!item.support_answer">本题暂不支持作答</view>
							</view>
						</view>

						<view class="options-layout margin-top-xs" v-if="item.type == 1 || item.type == 3"
							:style="cssData.questionContentCss">
							<view @tap="singleSelectTap" :class="
			                            (item.select_answer == 'A'
			                                ? item.is_correct == 1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : item.correctOption == 'A'
			                                ? item.select_answer && item.is_correct == 2
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : '') + ' layout-result'
			                        " data-answer="A" v-if="item.A">
								<text>A</text>
								<rich-text @longpress="onLongPress(item.A)" :nodes="item.A"></rich-text>
							</view>
							<view @tap="singleSelectTap" :class="
			                            (item.select_answer == 'B'
			                                ? item.is_correct == 1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : item.correctOption == 'B'
			                                ? item.select_answer && item.is_correct == 2
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : '') + ' layout-result'
			                        " data-answer="B" v-if="item.B">
								<text>B</text>
								<rich-text @longpress="onLongPress(item.B)" :nodes="item.B"></rich-text>
							</view>
							<view @tap="singleSelectTap" :class="
			                            (item.select_answer == 'C'
			                                ? item.is_correct == 1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : item.correctOption == 'C'
			                                ? item.select_answer && item.is_correct == 2
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : '') + ' layout-result'
			                        " data-answer="C" v-if="item.C">
								<text>C</text>
								<rich-text @longpress="onLongPress(item.C)" :nodes="item.C" :style="cssData.questionContentCss"></rich-text>
							</view>
							<view @tap="singleSelectTap" :class="
			                            (item.select_answer == 'D'
			                                ? item.is_correct == 1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : item.correctOption == 'D'
			                                ? item.select_answer && item.is_correct == 2
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : '') + ' layout-result'
			                        " data-answer="D" v-if="item.D">
								<text>D</text>
								<rich-text @longpress="onLongPress(item.D)" :nodes="item.D" :style="cssData.questionContentCss"></rich-text>
							</view>
							<view @tap="singleSelectTap" :class="
			                            (item.select_answer == 'E'
			                                ? item.is_correct == 1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : item.correctOption == 'E'
			                                ? item.select_answer && item.is_correct == 2
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : '') + ' layout-result'
			                        " data-answer="E" v-if="item.E">
								<text>E</text>
								<rich-text @longpress="onLongPress(item.E)" :nodes="item.E" :style="cssData.questionContentCss"></rich-text>
							</view>
							<view @tap="singleSelectTap" :class="
			                            (item.select_answer == 'F'
			                                ? item.is_correct == 1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : item.correctOption == 'F'
			                                ? item.select_answer && item.is_correct == 2
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : '') + ' layout-result'
			                        " data-answer="F" v-if="item.F">
								<text>F</text>
								<rich-text @longpress="onLongPress(item.F)" :nodes="item.F" :style="cssData.questionContentCss"></rich-text>
							</view>
						</view>
						<view class="options-layout margin-top-xs" v-else-if="item.type == 2"
							:style="cssData.questionContentCss">
							<view @tap="multipleSelectTap" :class="
			                            (item.is_correct == 0
			                                ? item.select_answer.indexOf('A') != -1
			                                    ? 'layout-result-select'
			                                    : ''
			                                : item.is_correct == 1
			                                ? item.select_answer.indexOf('A') != -1  && item.correctOptionArr.indexOf('A') != -1
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : item.select_answer.indexOf('A') != -1
			                                ? item.select_answer.indexOf('A') != -1  && item.correctOptionArr.indexOf('A') != -1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : '') + ' layout-result'
			                        " data-index="0" v-if="item.A">
								<text>A</text>
								<rich-text @longpress="onLongPress(item.A)" :nodes="item.A" :style="cssData.questionContentCss"></rich-text>
							</view>
							<view @tap="multipleSelectTap" :class="
			                            (item.is_correct == 0
			                                ? item.select_answer.indexOf('B') != -1
			                                    ? 'layout-result-select'
			                                    : ''
			                                : item.is_correct == 1
			                                ? item.select_answer.indexOf('B') != -1  && item.correctOptionArr.indexOf('B') != -1
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : item.select_answer.indexOf('B') != -1
			                                ? item.select_answer.indexOf('B') != -1  && item.correctOptionArr.indexOf('B') != -1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : '') + ' layout-result'
			                        " data-index="1" v-if="item.B">
								<text>B</text>
								<rich-text @longpress="onLongPress(item.B)" :nodes="item.B" :style="cssData.questionContentCss"></rich-text>
							</view>
							<view @tap="multipleSelectTap" :class="
			                            (item.is_correct == 0
			                                ? item.select_answer.indexOf('C') != -1
			                                    ? 'layout-result-select'
			                                    : ''
			                                : item.is_correct == 1
			                                ? item.select_answer.indexOf('C') != -1  && item.correctOptionArr.indexOf('C') != -1
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : item.select_answer.indexOf('C') != -1
			                                ? item.select_answer.indexOf('C') != -1  && item.correctOptionArr.indexOf('C') != -1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : '') + ' layout-result'
			                        " data-index="2" v-if="item.C">
								<text>C</text>
								<rich-text @longpress="onLongPress(item.C)" :nodes="item.C"></rich-text>
							</view>
							<view @tap="multipleSelectTap" :class="
			                            (item.is_correct == 0
			                                ? item.select_answer.indexOf('D') != -1
			                                    ? 'layout-result-select'
			                                    : ''
			                                : item.is_correct == 1
			                                ? item.select_answer.indexOf('D') != -1  && item.correctOptionArr.indexOf('D') != -1
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : item.select_answer.indexOf('D') != -1
			                                ? item.select_answer.indexOf('D') != -1  && item.correctOptionArr.indexOf('D') != -1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : '') + ' layout-result'
			                        " data-index="3" v-if="item.D">
								<text>D</text>
								<rich-text @longpress="onLongPress(item.D)" :nodes="item.D"></rich-text>
							</view>
							<view @tap="multipleSelectTap" :class="
			                            (item.is_correct == 0
			                                ? item.select_answer.indexOf('E') != -1
			                                    ? 'layout-result-select'
			                                    : ''
			                                : item.is_correct == 1
			                                ? item.select_answer.indexOf('E') != -1  && item.correctOptionArr.indexOf('E') != -1
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : item.select_answer.indexOf('E') != -1
			                                ? item.select_answer.indexOf('E') != -1  && item.correctOptionArr.indexOf('E') != -1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : '') + ' layout-result'
			                        " data-index="4" v-if="item.E">
								<text>E</text>
								<rich-text @longpress="onLongPress(item.E)" :nodes="item.E"></rich-text>
							</view>
							<view @tap="multipleSelectTap" :class="
			                            (item.is_correct == 0
			                                ? item.select_answer.indexOf('F') != -1
			                                    ? 'layout-result-select'
			                                    : ''
			                                : item.is_correct == 1
			                                ? item.select_answer.indexOf('F') != -1  && item.correctOptionArr.indexOf('F') != -1
			                                    ? 'layout-result-correct'
			                                    : ''
			                                : item.select_answer.indexOf('F') != -1
			                                ? item.select_answer.indexOf('F') != -1  && item.correctOptionArr.indexOf('F') != -1
			                                    ? 'layout-result-correct'
			                                    : 'layout-result-error'
			                                : '') + ' layout-result'
			                        " data-index="5" v-if="item.F">
								<text>F</text>
								<rich-text @longpress="onLongPress(item.F)" :nodes="item.F"></rich-text>
							</view>
							<view @tap="multipleSelectCommitTap" class="text-submit" :style="cssData.questionCommitCss"
								v-if="item.is_correct == 0">提交
							</view>
						</view>
						<view class="padding flex flex-direction" v-if="item.question_images.length > 0">
							<button @tap="openQuestionImagesTap" class="cu-btn  bg-blue"><text class="cuIcon-picfill"
									style="margin-right: 10rpx;"></text>查看题目中的图片</button>
						</view>
						<!-- 不支持作答的题目添加查看答案按钮 -->
						<view class="padding flex flex-direction" v-if="!item.support_answer && selectModelIndex == 0 && item.is_correct < 1">
							<button @tap="toggleUnsupportedAnswers" class="cu-btn bg-blue"><text class="cuIcon-attentionfill" style="margin-right: 10rpx;"></text>查看答案</button>
						</view>
						<view :style="cssData.questionAnswerCss" class="margin-buttom-xs" v-if="item.is_correct > 0">
							<view class="explain-layout margin-top-xs" v-if="item.support_answer || (selectModelIndex == 1) || item.is_correct >= 1">
								<view class="cu-bar bg-white" :style="cssData.questionOtherTitleCss" v-if="item.is_correct > 0">
									<view class="action" style="margin-left: 0;">
										<text class="cuIcon-titles text-blue" style="margin-right: 0;"></text> <text
											class=" text-black">答案</text>
									</view>
								</view>

								<view style="display: flex" v-if="item.is_correct > 0">
									<view class="explain-answer" :style="cssData.questionContentCss">
										<view v-if="item.type == 1 || item.type == 2 || item.type == 3">参考答案</view>
										<rich-text @longpress="onLongPress(item.correctOption)"
											:class="item.type == 1 || item.type == 2 || item.type == 3 ? 'correct' : 'answer'"
											:nodes="item.correctOption"></rich-text>
									</view>
									<view class="explain-answer margin-left" :style="cssData.questionContentCss"
										v-if="item.type == 1 || item.type == 2 || item.type == 3">
										<view>您的答案</view>
										<rich-text :class="item.is_correct == 1 ? 'correct' : 'error'"
											:nodes="Array.isArray(item.select_answer) ? item.select_answer.join('') : item.select_answer">
										</rich-text>
									</view>
								</view>
								<view class="cu-bar bg-white" :style="cssData.questionOtherTitleCss">
									<view class="action" style="margin-left: 0;">
										<text class="cuIcon-titles text-blue" style="margin-right: 0;"></text> <text
											class=" text-black">本题解析</text>
									</view>
									<view class="action">
										<button class="cu-btn bg-orange round shadow"
											@tap="aiExplanationTap(true)"><text class="cuIcon-new"
												style="padding-right: 2px;"></text> AI解题</button>
									</view>
								</view>
								<rich-text @longpress="onLongPress(item.explanation)" class="explain-text"
									:style="cssData.questionContentCss" :nodes="item.explanation"
									v-if="item.explanation">
								</rich-text>
							</view>
						
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
			<view class="cu-modal bottom-modal" :class="openAiExplanationModal==true  ?'show':''"
				@tap="aiExplanationTap(false)">
				<view class="cu-dialog" @tap.stop="">
					<view class="cu-bar bg-white justify-end">
						<view class="content">
							<text class="text-blod text-black">AI解题</text>
						</view>
					</view>
					<view class="padding-xl" style="padding: 30rpx;text-align: left;">
						<rich-text @longpress="onLongPress(listData[swiperCurrent].aiExplanation)" :nodes="listData[swiperCurrent].aiExplanation">
						</rich-text>
					</view>
				</view>
			</view>
			<view class="cu-modal bottom-modal " :class="[openSetModal==true ?'show':'']" @tap="onCloseSetTap">
				<view class="cu-dialog" @tap.stop="">
					<view class="cu-bar bg-white justify-between">
						<view class="content">
							<text class="text-bold text-black">工具箱</text>
						</view>
						<view class="action" @tap="onCloseSetTap">
							<text class="cuIcon-close text-gray"></text>
						</view>
					</view>
					
					<scroll-view scroll-x class="bg-white nav" style="border-bottom: 1rpx solid #f1f1f1;">
						<view class="flex text-center">
							<view class="cu-item flex-sub" :class="index==moreActionNavIndex?'text-blue cur':''"
								v-for="(item,index) in moreActionNavList" :key="index" @tap="moreActionIndexTap"
								:data-index="index">
								{{item}}
							</view>
						</view>
					</scroll-view>
					
					<view class="padding-bottom-xl">
						<!-- 题目操作 -->
						<view v-if="moreActionNavIndex==0" class="padding-sm">
							<!-- 题目操作 -->
							<view class="display-card margin-bottom">
								<view class="display-card-header">
									<text class="cuIcon-favor text-blue margin-right-xs"></text>
									<text class="text-bold">题目操作</text>
								</view>
								<view class="display-card-content">
									<view class="operation-grid">
										<view class="operation-btn" @tap="onCollectTap">
											<view class="operation-icon">
												<text class="cuIcon-likefill" :class="(listData[swiperCurrent] && listData[swiperCurrent].isCollection==1) ? 'text-red' : 'text-blue'"></text>
											</view>
											<view class="operation-text">{{(listData[swiperCurrent] && listData[swiperCurrent].isCollection==1) ? '取消收藏':'收藏题目'}}</view>
										</view>
										<view class="operation-btn" @tap="onFeedbackTap">
											<view class="operation-icon">
												<text class="cuIcon-warnfill text-orange"></text>
											</view>
											<view class="operation-text">题目纠错</view>
										</view>
										<view class="operation-btn" @tap="onCleanErrorQuestionTap">
											<view class="operation-icon">
												<text class="cuIcon-deletefill text-red"></text>
											</view>
											<view class="operation-text">清空错题</view>
										</view>
										<view class="operation-btn" @tap="onCleanQuestionTap">
											<view class="operation-icon">
												<text class="cuIcon-warnfill text-red"></text>
											</view>
											<view class="operation-text">重置记录</view>
										</view>
									</view>
								</view>
							</view>
						</view>
						
						<!-- 答题设置 -->
						<view v-if="moreActionNavIndex==1" class="padding-sm">
							<!-- 自动跳转设置 -->
							<view class="display-card margin-bottom">
								<view class="display-card-header">
									<text class="cuIcon-forward text-blue margin-right-xs"></text>
									<text class="text-bold">自动跳转设置</text>
								</view>
								<view class="display-card-content">
									<view class="cu-form-group">
										<view class="title">答题后自动跳转下一题</view>
										<switch @change="autoNextTap" :class="isAutoNext?'checked':''" :checked="isAutoNext"></switch>
									</view>
									<view class="cu-form-group">
										<view class="title">答题正确后自动跳转下一题</view>
										<switch @change="autoCorrectNextTap" :class="isAutoCorrectNext?'checked':''" :checked="isAutoCorrectNext"></switch>
									</view>
								</view>
							</view>
							
	
						</view>
						
						<!-- 显示设置 -->
						<view v-if="moreActionNavIndex==2" class="padding-sm">
							<!-- 夜间模式 -->
							<view class="display-card margin-bottom">
								<view class="display-card-header">
									<text class="cuIcon-moon text-yellow margin-right-xs"></text>
									<text class="text-bold">夜间模式</text>
								</view>
								<view class="display-card-content flex align-center justify-between">
									<view class="text-gray text-sm">保护眼睛，适合夜间阅读</view>
									<switch @change="toggleDarkMode" :checked="isDarkMode" color="#39b54a"></switch>
								</view>
							</view>
							
							<!-- 字体大小 -->
							<view class="display-card">
								<view class="display-card-header">
									<text class="cuIcon-font text-blue margin-right-xs"></text>
									<text class="text-bold">字体大小</text>
								</view>
								<view class="display-card-content">
									<view class="text-gray text-sm margin-bottom-sm">选择适合您的字体大小</view>
									<view class="font-size-slider">
										<view class="font-size-item" 
											v-for="(item, index) in selectFontList" 
											:key="index"
											:class="item.value==questionFont ? 'active' : ''"
											@tap="setQuestionFontTap({detail:{value:item.value}})">
											<text :style="'font-size:'+item.value">A</text>
											<view class="font-size-dot"></view>
										</view>
									</view>
									<view class="font-size-labels flex justify-between">
										<text class="text-xs text-gray">小</text>
										<text class="text-xs text-gray">默认</text>
										<text class="text-xs text-gray">大</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="bottom-layout cu-bar tabbar">
				<view class="action text-blue" @tap="onOpenSetTap">
					<view class="cuIcon-usefullfill"></view>
					工具
				</view>
				<view class="action text-blue" @tap="setQuestionDirectionTap(1)">
					<view class="cuIcon-roundleftfill-copy"></view>上一题
				</view>
				<view class="action text-blue add-action" @tap="commitTap">
					<button class="cu-btn cuIcon-write shadow bg-gradual-blue"></button>
					保存
				</view>
				<view class="action text-blue" @tap="setQuestionDirectionTap(2)" data-type="2">
					<view class="cuIcon-roundrightfill"></view>下一题
				</view>

				<view class="action text-blue" @tap="openAnswerCardTap">
					<view class="cuIcon-formfill"></view>
					答题卡
				</view>
			</view>
		</view>
		<!-- 答题卡弹窗 -->
		<view class="cu-modal bottom-modal" :class="openAnswerCardModal ? 'show' : ''" @tap="closeAnswerCardTap">
			<view class="cu-dialog answer-card-dialog" @tap.stop="">
				<view class="cu-bar bg-white justify-between">
					<view class="content">
						<text class="text-blod text-black">答题卡</text>
					</view>
					<view class="action" @tap="closeAnswerCardTap">
						<text class="cuIcon-close text-gray"></text>
					</view>
				</view>
				
				<!-- 答题进度信息 -->
				<view class="padding-sm bg-white flex align-center justify-between" style="border-bottom: 1rpx solid #eee;">
					<!-- 进度信息 (左侧) -->
					<view class="flex align-center">
						<view>
							<text class="text-bold text-lg">已答：<text class="text-blue">{{countStats[2]}}</text>/<text>{{ questionCount }}</text></text>
						</view>
					</view>
					
					<!-- 状态指示器 (右侧) -->
					<view class="flex align-center">
						<view class="margin-right-sm flex align-center">
							<view class="cu-tag bg-green round margin-right-xs" style="width: 40rpx; height: 40rpx; padding: 0; box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center;"><text class="cuIcon-check text-white" style="font-size: 24rpx;"></text></view>
							<text class="text-green">正确({{countStats[0]}})</text>
						</view>
						<view class="margin-right-sm flex align-center">
							<view class="cu-tag bg-red round margin-right-xs" style="width: 40rpx; height: 40rpx; padding: 0; box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1); display: flex; align-items: center; justify-content: center;"><text class="cuIcon-close text-white" style="font-size: 24rpx;"></text></view>
							<text class="text-red">错误({{countStats[1]}})</text>
						</view>
						<view class="flex align-center">
							<view class="cu-tag bg-white round margin-right-xs" style="width: 40rpx; height: 40rpx; padding: 0; box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1); border: 1px solid #e0e0e0;"></view>
							<text class="text-black">未答({{countStats[3]}})</text>
						</view>
					</view>
				</view>
				
				<!-- 答题卡按钮 - 使用滚动视图 -->
				<scroll-view scroll-y class="answer-card-scroll-view" show-scrollbar="true" enhanced="true" scroll-top="0">
					<view class="padding-sm bg-white answer-card-content">
						<view style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 15rpx; padding: 10rpx 0;">
							<button v-for="item in answerSheetStatus" :key="item.index"
								:class="[
									'cu-btn round answer-card-btn',
									item.status === 1 ? 'bg-green text-white' : 
									item.status === 2 ? 'bg-red text-white' : 
									'bg-white text-black'
								]"
								@tap="answerCardItemTap(item)"
							>
								{{ item.index }}
							</button>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</view>
</template>
<style src="./practice.css"></style>
<style>
/* 显示设置卡片样式 */

/* 显示设置卡片样式 */
.display-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
    overflow: hidden;
    margin: 20rpx 0;
}

.display-card-header {
    padding: 24rpx;
    border-bottom: 1rpx solid #f5f5f5;
    display: flex;
    align-items: center;
}

.display-card-content {
    padding: 24rpx;
}

/* 字体大小选择器样式 */
.font-size-slider {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    position: relative;
}

.font-size-slider:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    height: 4rpx;
    background-color: #eee;
    z-index: 1;
}

.font-size-item {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    background-color: #fff;
}

.font-size-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: #ddd;
    margin-top: 8rpx;
}

.font-size-item.active text {
    color: #0081ff;
    font-weight: bold;
}

.font-size-item.active .font-size-dot {
    background-color: #0081ff;
    width: 24rpx;
    height: 24rpx;
}

.font-size-labels {
    margin-top: 10rpx;
}

/* 操作按钮样式 */
.operation-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.operation-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    background-color: #f8f8f8;
    transition: all 0.3s;
    width: 48%;
    box-sizing: border-box;
}

.operation-btn:active {
    background-color: #e0e0e0;
}

.operation-icon {
    font-size: 48rpx;
    margin-bottom: 10rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.operation-text {
    font-size: 26rpx;
    color: #333;
    text-align: center;
}

/* 开关样式优化 */
.cu-switch {
    position: relative;
    width: 90rpx;
    height: 50rpx;
    border-radius: 100rpx;
    background-color: #ddd;
    transition: background-color 0.3s;
}

.cu-switch::before {
    content: "";
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 0 10rpx rgba(0,0,0,0.1);
    left: 5rpx;
    top: 5rpx;
    transition: transform 0.3s;
}

.cu-switch.checked {
    background-color: #39b54a;
}

.cu-switch.checked::before {
    transform: translateX(40rpx);
}

/* 答题卡弹窗样式 */
.answer-card-dialog {
    max-height: 80vh !important; /* 限制最大高度为视口高度的80% */
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
}

.answer-card-scroll-view {
    flex: 1;
    height: auto;
    min-height: 200rpx; /* 设置最小高度确保内容可见 */
    max-height: 60vh; /* 限制滚动区域的最大高度 */
    overflow-y: auto;
    position: relative;
    -webkit-overflow-scrolling: touch; /* 增加iOS滚动流畅度 */
}

.answer-card-content {
    padding-bottom: 30rpx !important; /* 确保底部有足够的空间 */
}

.answer-card-btn {
    width: 76rpx; 
    height: 76rpx; 
    margin: 5rpx auto; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    font-size: 32rpx; 
    font-weight: 500; 
    padding: 0; 
    box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1); 
    transition: all 0.2s ease; 
    border: 1px solid #e0e0e0;
}

/* 修复弹窗显示问题 */
.cu-modal.show {
    opacity: 1;
    transition-duration: 0.3s;
    overflow-y: visible;
    display: flex !important;
    align-items: flex-end;
}

.cu-modal.bottom-modal.show .cu-dialog {
    transform: translateY(0);
    transition-duration: 0.3s;
    overflow: visible;
}
</style>
<script src="./practice.js"></script>