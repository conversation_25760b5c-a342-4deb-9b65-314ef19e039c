import {
	get,
	post,
	upload
} from "@/common/js/http";

let that = null;
let app = getApp();
const recorderManager = uni.getRecorderManager();
export default {
	data() {
		return {
			keyword: '',
			course_id: 0,
			isBack: false,
			isHome: false,
			isLoad: false,
			recordList: [],
			isAudioing: false,
			appIsAudit: false,
			// 混合模式相关
			showWebView: false,
			webUrl: ''
		};
	},
	onLoad(options) {
		console.log(options);
		const {
			course_id = 0
		} = options || {};
		that = this;
		that.course_id = course_id;
		that.isBack = that.isHome = course_id > 0;
		this.checkScreenSize();
		//that.listenRecorderManager();
		that.searchRecord();
		app.globalData.showShareMenu();
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		listenRecorderManager() {
			recorderManager.onError((res) => {
				console.log('录音异常:');
				console.log(res);
				that.isAudioing = false;
				app.showToast(res.errMsg);
			});
			recorderManager.onStart((res) => {
				console.log('录音开始:');
				that.isAudioing = true;
			});
			recorderManager.onPause((res) => {
				console.log('录音暂停:');
			});
			recorderManager.onStop((res) => {
				console.log('录音结束:');
				console.log(res);
				that.isAudioing = false;
				// 文件上传OSS
				let filePath = res.tempFilePath;
				let formData = {
					'app': 'learnAppSearch'
				};
				upload('file', filePath, formData, {}).then((upRes) => {
					let upResData = JSON.parse(upRes.data)
					if (upResData.code == 0) {
						app.showToast(upResData.message);
						return;
					}

					// 文件进行语音识别
					let url = upResData.data;
					post('ai/speech', {
						url: url
					}).then((aiRes) => {
						that.keyword = aiRes.data.join(",");
						app.showToast("识别成功");
					});
				});
			});
			recorderManager.onInterruptionBegin((res) => {
				console.log('录音被系统中断:');
				that.isAudioing = false;
				console.log(res);
			});
		},
		audioActionTap(action) {
			recorderManager.stop();
			that.isAudioing = false;
		},
		audioTap() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				app.showToast('仅对邀请用户开放');
				return;
			}
			recorderManager.start();
		},
		async chooseImageTap() {
			let checkHasVip = app.checkHasVip();
			if (!checkHasVip) {
				app.showToast('仅对邀请用户开放');
				return;
			}

			// #ifdef MP-WEIXIN
			uni.chooseMedia({
				count: 1,
				mediaType: ['image'],
				sourceType: ['album', 'camera'],
				maxDuration: 30,
				async success(res) {
					await that.chooseImageOverTap(res);
				}
			})
			// #endif

			// #ifndef MP-WEIXIN
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				async success(res) {
					await that.chooseImageOverTap(res);
				}
			})
			// #endif
		},
		async chooseImageOverTap(res) {
			// 文件上传OSS
			let filePath = res.tempFiles[0].tempFilePath;
			let formData = {
				'app': 'learnAppSearch',
				'sign': app.globalData.getTimestamp()
			};
			let upRes = await upload('file', filePath, formData, {});

			// 文件进行OCR识别
			let url = upRes.data;
			let aiRes = await post('ai/ocr', {
				url: url
			});
			that.keyword = aiRes.data.join(",");
			app.showToast("识别成功");
		},
		clearTap() {
			that.keyword = '';
		},
		clearAndPasteTap() {
			that.keyword = '';
			uni.getClipboardData({
				success: (res) => {
					that.keyword = res.data;
				}
			});
		},
		onInputChange(options) {
			that.keyword = options.detail.value;
		},
		onSearchClick() {
			if (that.keyword == '') {
				app.showToast('请输入搜索关键字');
				return;
			}
			let url = '../search/list?keyword=' + that.keyword;
			uni.navigateTo({
				url: url
			});
		},
		onClickQuestion(id) {
			let url = '../practice/question/detail?id=';
			uni.navigateTo({
				url: url + id
			});
		},
		searchRecord() {
			app.globalData.server
				.getRequest('question/searchRecord', {})
				.then(function(res) {
					console.log(res);
					that.recordList = res.data.list;
					that.isLoad = true;
				})
				.catch(function(res) {
					console.log(res);
				});
		},
		// 检测屏幕尺寸
		checkScreenSize() {
			try {
				const systemInfo = uni.getSystemInfoSync();
				const { windowWidth, windowHeight, platform, system } = systemInfo;
				const aspectRatio = windowWidth / windowHeight;

				const shouldShowWeb =
					windowWidth > 600 ||
					aspectRatio > 1.0 ||
					platform === 'windows' ||
					platform === 'mac' ||
					(system && (system.includes('Windows') || system.includes('Mac')));

				if (shouldShowWeb) {
					this.showWebView = true;
					this.webUrl = this.buildWebUrl();
				} else {
					this.showWebView = false;
				}
			} catch (error) {
				this.showWebView = true;
				this.webUrl = this.buildWebUrl();
			}
		},
		// 构建Web端URL
		buildWebUrl() {
			const baseUrl = 'https://www.beikeshuati.com';
			const url = new URL('/search', baseUrl);

			// 添加小程序标识
			url.searchParams.set('from', 'miniprogram');
			url.searchParams.set('platform', 'wechat');

			// 添加课程ID
			if (this.course_id) {
				url.searchParams.set('course_id', this.course_id);
			}

			return url.toString();
		},
		// 处理Web端消息
		handleWebMessage(e) {
			console.log('收到Web端消息:', e.detail.data);
		},
		// Web端加载完成
		handleWebLoad() {
			console.log('Web端加载完成');
		},
		// Web端加载错误
		handleWebError(e) {
			console.error('Web端加载错误:', e);
			// 加载失败时切换到原生模式
			this.showWebView = false;
			uni.showToast({
				title: '页面加载失败，已切换到原生模式',
				icon: 'none'
			});
		}
	}
};