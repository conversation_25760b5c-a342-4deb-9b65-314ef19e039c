import responsiveMixin from '@/mixins/responsive.js';

let that = null;
let app = getApp();
export default {
	mixins: [responsiveMixin],
	data() {
		return {
			load: false,
			user: [],
			appPlatform: app.globalData.appPlatform,
			menuList: [
				{ name: '个人信息', icon: 'cuIcon-profile', url: '/pages/user/info/info' },
				{ name: '会员中心', icon: 'cuIcon-vip', url: '/pages/user/vip/vip' },
				{ name: '积分签到', icon: 'cuIcon-rechargefill', url: '/pages/user/sign/sign' },
				{ name: '推广中心', icon: 'cuIcon-share', url: '/pages/user/promote/promote' },
				{ name: '排行榜', icon: 'cuIcon-rank', url: '/pages/user/ranking/ranking' },
				{ name: '切换账号', icon: 'cuIcon-people', url: '/pages/user/switch/switch' },
				{ name: '关于我们', icon: 'cuIcon-info', url: '/pages/user/about/about' }
			],
			paths: {
				"info": "/pages/user/info/info",
				"about": "/pages/user/about/about",
				"switch": "/pages/user/switch/switch",
				'promote': "/pages/user/promote/promote",
				"ranking": "/pages/user/ranking/ranking",
				"customer": "/pages/user/customer/customer",
				"score": "/pages/user/sign/sign",
				"rechargeMember": "/pages/user/vip/vip",
				"wechat": "/pages/user/login/bindopenid?from=2",
				"email": "/pages/user/login/bindemail",
				"mobile": "/pages/user/info/edit",
				"opinion": "/pages/user/opinion/opinion",
				"password": "/pages/user/set/password",
				"myCourse": "/pages/user/course/course",
				"myCollect": "/pages/practice/coll/coll",
				"myErrorQuestion": "/pages/practice/error/error",
			},
			appIsAudit: false,
			isIosVirtualPay: true,
			showOnlineServiceNotice: false
		};
	},
	onLoad() {
		that = this;
		app.globalData.showShareMenu();
	},
	onShow() {
		that.getUserInfo();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		getUserInfo() {
			app.globalData.server
				.getRequest('user/info', {})
				.then((e) => {
					app.globalData.config.storage.setUserInfoData(e.data);
					that.setData({
						user: e.data,
						appIsAudit: app.globalData.checkAppIsAudit(),		
						load: true
					});
					that.isIosVirtualPay = app.globalData.checkIsIosVirtualPay();
				})
				.catch((e) => {
					console.log(e);
				});
		},
		copyTap(data) {
			console.log(data);
			uni.setClipboardData({
				data: data.toString(),
				success() {
					app.showToast('复制成功');
				},
				fail(res) {
					console.log(res);
				}
			});
		},
		menuTap(options) {
			let url = options.currentTarget.dataset.url;
			console.log(url)
			uni.navigateTo({
				url: url
			});
		},
		showOnlineServiceTap() {
			that.showOnlineServiceNotice = true;
		},
	}
};