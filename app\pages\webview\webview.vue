<template>
  <hybrid-view
    :web-url="webUrl"
    :force-mode="forceMode"
    :debug-mode="true"
    :url-params="urlParams"
    @modeChange="handleModeChange"
    @webMessage="handleWebMessage"
    @webLoad="handleWebLoad"
    @webError="handleWebError">

    <!-- 原生小程序内容 -->
    <view class="native-content">
      <back :showBackText="false" :showBackIcon="true" :showBackLeft="true" :showHomeIcon="false"
        customClass="bg-gradual-blue text-white" title="混合模式"></back>

      <view class="content-wrapper">
        <view class="mode-info">
          <text class="mode-title">当前模式：{{ currentMode }}</text>
          <text class="mode-desc">{{ modeDescription }}</text>
        </view>

        <view class="control-panel">
          <button class="cu-btn bg-blue margin-sm" @tap="switchMode('auto')">自动模式</button>
          <button class="cu-btn bg-green margin-sm" @tap="switchMode('web')">强制Web端</button>
          <button class="cu-btn bg-orange margin-sm" @tap="switchMode('native')">强制原生</button>
        </view>

        <view class="info-panel">
          <view class="info-item">
            <text class="info-label">Web URL:</text>
            <text class="info-value">{{ webUrl }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">屏幕尺寸:</text>
            <text class="info-value">{{ screenSize }}</text>
          </view>
        </view>

        <!-- 原生小程序的其他内容可以放在这里 -->
        <view class="native-features">
          <text class="feature-title">原生小程序功能</text>
          <view class="feature-list">
            <view class="feature-item" @tap="goToPage('/pages/index/index')">
              <text class="cuIcon-home"></text>
              <text>首页</text>
            </view>
            <view class="feature-item" @tap="goToPage('/pages/search/search')">
              <text class="cuIcon-search"></text>
              <text>搜索</text>
            </view>
            <view class="feature-item" @tap="goToPage('/pages/user/user')">
              <text class="cuIcon-people"></text>
              <text>我的</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </hybrid-view>
</template>

<script>
import HybridView from '@/components/hybrid-view/hybrid-view.vue'
import hybridUtils from '@/common/js/hybrid-utils.js'

export default {
  components: {
    HybridView
  },

  data() {
    return {
      webUrl: '',
      forceMode: 'auto', // 'auto', 'web', 'native'
      currentMode: '自动',
      screenSize: '',
      pageName: 'index', // 默认页面
      urlParams: {}
    }
  },

  onLoad(options) {
    // 从URL参数获取配置
    if (options.webUrl) {
      this.webUrl = decodeURIComponent(options.webUrl)
    }
    if (options.pageName) {
      this.pageName = options.pageName
    }
    if (options.forceMode) {
      this.forceMode = options.forceMode
    }

    // 如果没有提供webUrl，则根据pageName生成
    if (!this.webUrl && this.pageName) {
      this.webUrl = hybridUtils.getPageUrl(this.pageName, this.urlParams)
    }

    // 初始化混合工具
    hybridUtils.init()
  },

  computed: {
    modeDescription() {
      switch(this.forceMode) {
        case 'web': return '强制显示Web端界面'
        case 'native': return '强制显示原生小程序界面'
        default: return '根据屏幕大小自动切换'
      }
    }
  },

  methods: {
    // 处理模式变化
    handleModeChange(data) {
      this.currentMode = data.mode === 'web' ? 'Web端' : '原生'
      this.screenSize = `${data.screenInfo.windowWidth}x${data.screenInfo.windowHeight}`
      console.log('模式切换:', data)
    },

    // 处理web端消息
    handleWebMessage(data) {
      console.log('收到Web端消息:', data)
      // 使用混合工具处理消息
      hybridUtils.handleWebMessage(data)
    },

    // web端加载完成
    handleWebLoad() {
      console.log('Web端加载完成')
      uni.showToast({
        title: 'Web端加载成功',
        icon: 'success'
      })
    },

    // web端加载错误
    handleWebError(error) {
      console.error('Web端加载错误:', error)
      uni.showToast({
        title: 'Web端加载失败',
        icon: 'none'
      })
    },

    // 切换模式
    switchMode(mode) {
      this.forceMode = mode
      uni.showToast({
        title: `切换到${this.modeDescription}`,
        icon: 'none'
      })
    },

    // 跳转页面
    goToPage(url) {
      uni.navigateTo({
        url: url
      })
    }
  }
}
</script>

<style scoped>
.native-content {
  width: 100%;
  height: 100vh;
  background: #f8f9fa;
}

.content-wrapper {
  padding: 20rpx;
}

.mode-info {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.mode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.mode-desc {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.control-panel {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  text-align: center;
}

.info-panel {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.info-item {
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
  display: block;
}

.native-features {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.feature-item {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.feature-item text:first-child {
  font-size: 40rpx;
  color: #007aff;
}

.feature-item text:last-child {
  font-size: 24rpx;
  color: #333;
}
</style>
