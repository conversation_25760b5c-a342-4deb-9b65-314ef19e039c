{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/hybrid-view/hybrid-view.vue?2ee6", "webpack:///D:/桌面/thinker/app/components/hybrid-view/hybrid-view.vue?5cda", "webpack:///D:/桌面/thinker/app/components/hybrid-view/hybrid-view.vue?b2fb", "webpack:///D:/桌面/thinker/app/components/hybrid-view/hybrid-view.vue?aee7", "uni-app:///components/hybrid-view/hybrid-view.vue", "webpack:///D:/桌面/thinker/app/components/hybrid-view/hybrid-view.vue?4ebb", "webpack:///D:/桌面/thinker/app/components/hybrid-view/hybrid-view.vue?fb0d"], "names": ["name", "props", "webUrl", "type", "default", "forceMode", "largeScreenWidth", "aspectRatioThreshold", "debugMode", "urlParams", "data", "screenInfo", "showDebugInfo", "computed", "screenRatio", "windowWidth", "windowHeight", "showWebView", "computedWebUrl", "url", "Object", "mounted", "methods", "initScreenInfo", "console", "mode", "handleWebMessage", "handleWebLoad", "handleWebError", "uni", "title", "icon", "refreshScreenInfo", "toggleDebugMode"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC2BnrB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;QAAA;MAAA;IACA;EACA;EAEAM;IACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QAAA;QAAAC;QAAA;QAAAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QAAAF;MACA,8CACA;IACA;IAEA;IACAG;MAAA;MACA;MAEA;;MAEA;MACAC;MACAA;;MAEA;MACAA;MACAA;;MAEA;MACAC;QACAD;MACA;MAEA;IACA;EACA;EAEAE;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;QACAC;;QAEA;QACA;UACAC;UACAd;QACA;MACA;QACAa;MACA;IACA;IAEA;IACAE;MACAF;MACA;IACA;IAEA;IACAG;MACAH;MACA;IACA;IAEA;IACAI;MACAJ;MACA;MAEAK;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpKA;AAAA;AAAA;AAAA;AAAg+B,CAAgB,07BAAG,EAAC,C;;;;;;;;;;;ACAp/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/hybrid-view/hybrid-view.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./hybrid-view.vue?vue&type=template&id=17dfbb32&scoped=true&\"\nvar renderjs\nimport script from \"./hybrid-view.vue?vue&type=script&lang=js&\"\nexport * from \"./hybrid-view.vue?vue&type=script&lang=js&\"\nimport style0 from \"./hybrid-view.vue?vue&type=style&index=0&id=17dfbb32&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17dfbb32\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/hybrid-view/hybrid-view.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-view.vue?vue&type=template&id=17dfbb32&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.showDebugInfo ? _vm.screenRatio.toFixed(2) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-view.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-view.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"hybrid-container\">\n    <!-- 大屏模式：显示web端 -->\n    <web-view \n      v-if=\"showWebView\" \n      :src=\"computedWebUrl\" \n      @message=\"handleWebMessage\"\n      @load=\"handleWebLoad\"\n      @error=\"handleWebError\"\n      class=\"web-view\">\n    </web-view>\n    \n    <!-- 小屏模式：显示原生内容 -->\n    <view v-else class=\"native-view\">\n      <slot></slot>\n    </view>\n    \n    <!-- 调试信息（开发环境显示） -->\n    <view v-if=\"showDebugInfo\" class=\"debug-info\">\n      <text>屏幕: {{screenInfo.windowWidth}}x{{screenInfo.windowHeight}}</text>\n      <text>模式: {{showWebView ? 'Web端' : '原生'}}</text>\n      <text>比例: {{screenRatio.toFixed(2)}}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'HybridView',\n  props: {\n    // web端URL\n    webUrl: {\n      type: String,\n      default: ''\n    },\n    // 强制模式：'web', 'native', 'auto'\n    forceMode: {\n      type: String,\n      default: 'auto'\n    },\n    // 大屏阈值\n    largeScreenWidth: {\n      type: Number,\n      default: 768\n    },\n    // 宽高比阈值\n    aspectRatioThreshold: {\n      type: Number,\n      default: 1.2\n    },\n    // 是否显示调试信息\n    debugMode: {\n      type: Boolean,\n      default: false\n    },\n    // URL参数\n    urlParams: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      screenInfo: {},\n      showDebugInfo: false\n    }\n  },\n  \n  computed: {\n    // 屏幕宽高比\n    screenRatio() {\n      const { windowWidth = 0, windowHeight = 1 } = this.screenInfo\n      return windowWidth / windowHeight\n    },\n    \n    // 是否显示web端\n    showWebView() {\n      if (this.forceMode === 'web') return true\n      if (this.forceMode === 'native') return false\n      \n      const { windowWidth = 0 } = this.screenInfo\n      return windowWidth > this.largeScreenWidth || \n             this.screenRatio > this.aspectRatioThreshold\n    },\n    \n    // 计算后的web URL\n    computedWebUrl() {\n      if (!this.webUrl) return ''\n      \n      const url = new URL(this.webUrl)\n      \n      // 添加小程序标识\n      url.searchParams.set('from', 'miniprogram')\n      url.searchParams.set('platform', 'wechat')\n      \n      // 添加屏幕信息\n      url.searchParams.set('screenWidth', this.screenInfo.windowWidth)\n      url.searchParams.set('screenHeight', this.screenInfo.windowHeight)\n      \n      // 添加自定义参数\n      Object.keys(this.urlParams).forEach(key => {\n        url.searchParams.set(key, this.urlParams[key])\n      })\n      \n      return url.toString()\n    }\n  },\n  \n  mounted() {\n    this.initScreenInfo()\n    this.showDebugInfo = this.debugMode\n  },\n  \n  methods: {\n    // 初始化屏幕信息\n    initScreenInfo() {\n      try {\n        this.screenInfo = uni.getSystemInfoSync()\n        console.log('屏幕信息:', this.screenInfo)\n        \n        // 触发模式变化事件\n        this.$emit('modeChange', {\n          mode: this.showWebView ? 'web' : 'native',\n          screenInfo: this.screenInfo\n        })\n      } catch (error) {\n        console.error('获取屏幕信息失败:', error)\n      }\n    },\n    \n    // 处理web端消息\n    handleWebMessage(e) {\n      console.log('Web端消息:', e.detail.data)\n      this.$emit('webMessage', e.detail.data)\n    },\n    \n    // web端加载完成\n    handleWebLoad(e) {\n      console.log('Web端加载完成')\n      this.$emit('webLoad', e)\n    },\n    \n    // web端加载错误\n    handleWebError(e) {\n      console.error('Web端加载错误:', e)\n      this.$emit('webError', e)\n      \n      uni.showToast({\n        title: '页面加载失败',\n        icon: 'none'\n      })\n    },\n    \n    // 手动刷新屏幕信息\n    refreshScreenInfo() {\n      this.initScreenInfo()\n    },\n    \n    // 切换调试模式\n    toggleDebugMode() {\n      this.showDebugInfo = !this.showDebugInfo\n    }\n  }\n}\n</script>\n\n<style scoped>\n.hybrid-container {\n  width: 100%;\n  height: 100vh;\n  position: relative;\n}\n\n.web-view {\n  width: 100%;\n  height: 100%;\n}\n\n.native-view {\n  width: 100%;\n  height: 100%;\n}\n\n.debug-info {\n  position: fixed;\n  top: 20rpx;\n  right: 20rpx;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 20rpx;\n  border-radius: 10rpx;\n  font-size: 24rpx;\n  z-index: 9999;\n}\n\n.debug-info text {\n  display: block;\n  margin-bottom: 10rpx;\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-view.vue?vue&type=style&index=0&id=17dfbb32&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-view.vue?vue&type=style&index=0&id=17dfbb32&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753607045629\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}