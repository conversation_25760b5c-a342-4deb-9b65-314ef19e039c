{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?6c65", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?6dc9", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?24fd", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?3d58", "uni-app:///components/responsive-card/responsive-card.vue", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?535c", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?4b8d"], "names": ["name", "mixins", "props", "shadow", "type", "default", "padding", "radius", "hover", "clickable", "computed", "cardClasses", "cardStyle", "borderRadius", "methods", "handleTap"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACOvrB;;;;;;;eAEA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACAC;MACA,QACA,mBACA,yCACA,cACA;QACA;QACA;MACA,EACA;IACA;IACAC;MACA;MACA;MAEA;QACAN;QACAO;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-card/responsive-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-card.vue?vue&type=template&id=ab6b2dd0&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-card.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-card.vue?vue&type=style&index=0&id=ab6b2dd0&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ab6b2dd0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-card/responsive-card.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=template&id=ab6b2dd0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=script&lang=js&\"", "<template>\n  <view :class=\"cardClasses\" :style=\"cardStyle\" @tap=\"handleTap\">\n    <slot></slot>\n  </view>\n</template>\n\n<script>\nimport responsiveMixin from '@/mixins/responsive.js';\n\nexport default {\n  name: 'ResponsiveCard',\n  mixins: [responsiveMixin],\n  props: {\n    shadow: {\n      type: String,\n      default: 'normal' // none, light, normal, heavy\n    },\n    padding: {\n      type: [Number, String],\n      default: 20\n    },\n    radius: {\n      type: [Number, String],\n      default: 8\n    },\n    hover: {\n      type: Boolean,\n      default: true\n    },\n    clickable: {\n      type: Boolean,\n      default: false\n    }\n  },\n  computed: {\n    cardClasses() {\n      return [\n        'responsive-card',\n        this.responsiveClasses,\n        `shadow-${this.shadow}`,\n        {\n          'card-hover': this.hover,\n          'card-clickable': this.clickable\n        }\n      ];\n    },\n    cardStyle() {\n      const padding = this.getResponsiveSpacing(this.padding);\n      const radius = typeof this.radius === 'number' ? `${this.radius}rpx` : this.radius;\n      \n      return {\n        padding: `${padding}rpx`,\n        borderRadius: radius\n      };\n    }\n  },\n  methods: {\n    handleTap(e) {\n      if (this.clickable) {\n        this.$emit('tap', e);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.responsive-card {\n  background: #fff;\n  transition: all 0.3s ease;\n  box-sizing: border-box;\n}\n\n.shadow-none {\n  box-shadow: none;\n}\n\n.shadow-light {\n  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.1);\n}\n\n.shadow-normal {\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.shadow-heavy {\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);\n}\n\n.card-hover:hover {\n  transform: translateY(-2rpx);\n  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.15);\n}\n\n.card-clickable {\n  cursor: pointer;\n}\n\n.card-clickable:active {\n  transform: translateY(1rpx);\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=style&index=0&id=ab6b2dd0&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=style&index=0&id=ab6b2dd0&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753611117459\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}