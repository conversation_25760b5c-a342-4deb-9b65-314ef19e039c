{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/桌面/thinker/app/pages/hybrid-demo/hybrid-demo.vue?6176", "webpack:///D:/桌面/thinker/app/pages/hybrid-demo/hybrid-demo.vue?f74d", "webpack:///D:/桌面/thinker/app/pages/hybrid-demo/hybrid-demo.vue?bdcf", "uni-app:///pages/hybrid-demo/hybrid-demo.vue", "webpack:///D:/桌面/thinker/app/pages/hybrid-demo/hybrid-demo.vue?9f72", "webpack:///D:/桌面/thinker/app/pages/hybrid-demo/hybrid-demo.vue?0ca5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentMode", "screenInfo", "config", "availablePages", "computed", "aspectRatio", "windowWidth", "windowHeight", "recommendedMode", "onLoad", "onShow", "methods", "initData", "hybridUtils", "updateScreenInfo", "switchMode", "uni", "title", "icon", "getModeText", "openPage", "content", "success", "navigateToPage", "forceMode", "params", "demo", "timestamp"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+FnrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;QAAA;QAAAC;QAAA;QAAAC;MACA;IACA;IAEAC;MACA;MAEA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAD;MACA;IACA;IAEA;IACAE;MACA;MACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;QACAJ;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAI;UACAC;YACA;cACA;YACA;UACA;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACAV;QACAW;QACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAAg+B,CAAgB,07BAAG,EAAC,C;;;;;;;;;;;ACAp/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/hybrid-demo/hybrid-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/hybrid-demo/hybrid-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./hybrid-demo.vue?vue&type=template&id=ea454ca0&scoped=true&\"\nvar renderjs\nimport script from \"./hybrid-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./hybrid-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./hybrid-demo.vue?vue&type=style&index=0&id=ea454ca0&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ea454ca0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/hybrid-demo/hybrid-demo.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-demo.vue?vue&type=template&id=ea454ca0&scoped=true&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"demo-container\">\n    <back :showBackText=\"false\" :showBackIcon=\"true\" :showBackLeft=\"true\" :showHomeIcon=\"false\"\n      customClass=\"bg-gradual-blue text-white\" title=\"混合模式演示\"></back>\n    \n    <view class=\"demo-content\">\n      <!-- 模式选择 -->\n      <view class=\"mode-selector\">\n        <view class=\"selector-title\">选择显示模式</view>\n        <view class=\"selector-buttons\">\n          <button \n            class=\"cu-btn margin-sm\"\n            :class=\"currentMode === 'auto' ? 'bg-blue' : 'line-blue'\"\n            @tap=\"switchMode('auto')\">\n            自动模式\n          </button>\n          <button \n            class=\"cu-btn margin-sm\"\n            :class=\"currentMode === 'web' ? 'bg-green' : 'line-green'\"\n            @tap=\"switchMode('web')\">\n            Web端\n          </button>\n          <button \n            class=\"cu-btn margin-sm\"\n            :class=\"currentMode === 'native' ? 'bg-orange' : 'line-orange'\"\n            @tap=\"switchMode('native')\">\n            原生端\n          </button>\n        </view>\n      </view>\n      \n      <!-- 页面选择 -->\n      <view class=\"page-selector\">\n        <view class=\"selector-title\">选择要演示的页面</view>\n        <view class=\"page-list\">\n          <view \n            v-for=\"(page, key) in availablePages\" \n            :key=\"key\"\n            class=\"page-item\"\n            @tap=\"openPage(key)\">\n            <view class=\"page-info\">\n              <text class=\"page-name\">{{ page.title }}</text>\n              <text class=\"page-desc\">{{ page.enableHybrid ? '支持混合模式' : '仅原生模式' }}</text>\n            </view>\n            <text class=\"cuIcon-right text-gray\"></text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 当前状态 -->\n      <view class=\"status-panel\">\n        <view class=\"status-title\">当前状态</view>\n        <view class=\"status-info\">\n          <view class=\"status-item\">\n            <text class=\"status-label\">屏幕尺寸:</text>\n            <text class=\"status-value\">{{ screenInfo.windowWidth }}x{{ screenInfo.windowHeight }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"status-label\">宽高比:</text>\n            <text class=\"status-value\">{{ aspectRatio }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"status-label\">推荐模式:</text>\n            <text class=\"status-value\">{{ recommendedMode }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"status-label\">当前模式:</text>\n            <text class=\"status-value\">{{ currentMode }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 配置信息 -->\n      <view class=\"config-panel\">\n        <view class=\"config-title\">配置信息</view>\n        <view class=\"config-info\">\n          <view class=\"config-item\">\n            <text class=\"config-label\">大屏阈值:</text>\n            <text class=\"config-value\">{{ config.screen.largeScreenWidth }}px</text>\n          </view>\n          <view class=\"config-item\">\n            <text class=\"config-label\">宽高比阈值:</text>\n            <text class=\"config-value\">{{ config.screen.aspectRatioThreshold }}</text>\n          </view>\n          <view class=\"config-item\">\n            <text class=\"config-label\">Web端URL:</text>\n            <text class=\"config-value\">{{ config.web.currentUrl }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport hybridUtils from '@/common/js/hybrid-utils.js'\n\nexport default {\n  data() {\n    return {\n      currentMode: 'auto',\n      screenInfo: {},\n      config: {},\n      availablePages: {}\n    }\n  },\n  \n  computed: {\n    aspectRatio() {\n      const { windowWidth = 0, windowHeight = 1 } = this.screenInfo\n      return (windowWidth / windowHeight).toFixed(2)\n    },\n    \n    recommendedMode() {\n      if (!this.screenInfo.windowWidth) return '未知'\n      \n      const shouldUseWeb = hybridUtils.shouldUseWeb('index', 'auto')\n      return shouldUseWeb ? 'Web端' : '原生端'\n    }\n  },\n  \n  onLoad() {\n    this.initData()\n  },\n  \n  onShow() {\n    this.updateScreenInfo()\n  },\n  \n  methods: {\n    // 初始化数据\n    initData() {\n      hybridUtils.init()\n      this.config = hybridUtils.getConfig()\n      this.availablePages = this.config.pages\n      this.updateScreenInfo()\n    },\n    \n    // 更新屏幕信息\n    updateScreenInfo() {\n      hybridUtils.updateScreenInfo()\n      this.screenInfo = hybridUtils.screenInfo || {}\n    },\n    \n    // 切换模式\n    switchMode(mode) {\n      this.currentMode = mode\n      uni.showToast({\n        title: `切换到${this.getModeText(mode)}`,\n        icon: 'none'\n      })\n    },\n    \n    // 获取模式文本\n    getModeText(mode) {\n      switch(mode) {\n        case 'web': return 'Web端模式'\n        case 'native': return '原生模式'\n        default: return '自动模式'\n      }\n    },\n    \n    // 打开页面\n    openPage(pageName) {\n      const pageConfig = this.availablePages[pageName]\n      if (!pageConfig) {\n        uni.showToast({\n          title: '页面配置不存在',\n          icon: 'none'\n        })\n        return\n      }\n      \n      if (!pageConfig.enableHybrid && this.currentMode === 'web') {\n        uni.showModal({\n          title: '提示',\n          content: '该页面不支持Web端模式，将使用原生模式打开',\n          success: (res) => {\n            if (res.confirm) {\n              this.navigateToPage(pageName, 'native')\n            }\n          }\n        })\n        return\n      }\n      \n      this.navigateToPage(pageName, this.currentMode)\n    },\n    \n    // 导航到页面\n    navigateToPage(pageName, mode) {\n      hybridUtils.navigateTo(pageName, {\n        forceMode: mode,\n        params: {\n          demo: true,\n          timestamp: Date.now()\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.demo-container {\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.demo-content {\n  padding: 20rpx;\n}\n\n.mode-selector,\n.page-selector,\n.status-panel,\n.config-panel {\n  background: white;\n  margin-bottom: 20rpx;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.selector-title,\n.status-title,\n.config-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.selector-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 10rpx;\n}\n\n.page-list {\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.page-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 10rpx;\n}\n\n.page-info {\n  flex: 1;\n}\n\n.page-name {\n  font-size: 28rpx;\n  color: #333;\n  display: block;\n  margin-bottom: 5rpx;\n}\n\n.page-desc {\n  font-size: 24rpx;\n  color: #666;\n  display: block;\n}\n\n.status-info,\n.config-info {\n  display: flex;\n  flex-direction: column;\n  gap: 15rpx;\n}\n\n.status-item,\n.config-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.status-label,\n.config-label {\n  font-size: 28rpx;\n  color: #666;\n}\n\n.status-value,\n.config-value {\n  font-size: 28rpx;\n  color: #333;\n  font-weight: bold;\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-demo.vue?vue&type=style&index=0&id=ea454ca0&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hybrid-demo.vue?vue&type=style&index=0&id=ea454ca0&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753607226632\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}