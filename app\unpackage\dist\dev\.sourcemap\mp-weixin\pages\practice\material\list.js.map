{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/practice/material/list.vue?4394", "webpack:///D:/桌面/thinker/app/pages/practice/material/list.vue?f0df", "webpack:///D:/桌面/thinker/app/pages/practice/material/list.vue?2c37", "webpack:///D:/桌面/thinker/app/pages/practice/material/list.vue?023c", "uni-app:///pages/practice/material/list.vue", "webpack:///D:/桌面/thinker/app/pages/practice/material/list.vue?c75c", "webpack:///D:/桌面/thinker/app/pages/practice/material/list.vue?2218"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "id", "isLoad", "appIsAudit", "dataList", "onLoad", "that", "methods", "itemTap", "console", "uni", "url", "shareTap", "getList", "app", "catch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACa;AACyB;;;AAGzF;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,spBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqD3rB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAA;IACAA;EACA;EACAC;IACAC;MACAC;MACA;MACA;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACAF;QACAC;MACA;IACA;IACAE;MACA;QACAP;QACA;MACA;MACAQ;QACAR;QACAA;MACA,GACAS;QACAD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAAkyC,CAAgB,6rCAAG,EAAC,C;;;;;;;;;;;ACAtzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/practice/material/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/practice/material/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=487aa56b&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.css?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./list.vue?vue&type=style&index=1&id=487aa56b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"487aa56b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/practice/material/list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=487aa56b&scoped=true&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    empty: function () {\n      return import(\n        /* webpackChunkName: \"components/empty/empty\" */ \"@/components/empty/empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.isLoad ? _vm.dataList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"material-list-ios\">\r\n    <!-- 顶部导航栏 -->\r\n    <back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"学习资料\"></back>\r\n\r\n    <view class=\"list-content\" v-if=\"isLoad\">\r\n      <scroll-view scroll-y class=\"scroll-area\">\r\n        <!-- 空状态 -->\r\n        <empty v-if=\"dataList.length == 0\" info=\"暂时还没有资料\" :showAd=\"false\"></empty>\r\n        <!-- 资料卡片列表 -->\r\n        <view v-else>\r\n          <view class=\"material-card shadow\" v-for=\"(item, index) in dataList\" :key=\"index\" :data-id=\"item.id\" @tap=\"itemTap\">\r\n            <view class=\"card-main\">\r\n              <view class=\"card-title-row\">\r\n                <text class=\"cuIcon-file text-blue\"></text>\r\n                <text class=\"card-title\">{{ item.name }}</text>\r\n              </view>\r\n              <view class=\"card-tags\">\r\n                <view class=\"cu-tag bg-green light price-tag\">\r\n                  <text>{{ item.price }}</text>\r\n                  <text class=\"cuIcon-coin text-yellow\" style=\"margin-left: 4rpx; font-size: 24rpx;\"></text>\r\n                </view>\r\n                <view class=\"cu-tag bg-blue light version-tag\">\r\n                  {{ item.version == 1 ? '在线文档' : '网盘资料' }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view class=\"card-meta-row\">\r\n              <view class=\"meta-item\">\r\n                <text class=\"cuIcon-hotfill text-red\"></text>\r\n                <text class=\"meta-label\">兑换人数</text>\r\n                <text class=\"meta-value center-value\">{{ item.hot }}</text>\r\n              </view>\r\n              <view class=\"meta-action\">\r\n                <button class=\"cu-btn bg-blue radius detail-btn\">查看详情</button>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <!-- 底部悬浮分享按钮 -->\r\n    <view class=\"float-action-container\" v-if=\"isLoad && !appIsAudit\">\r\n      <view class=\"share-button\" @tap=\"shareTap\">\r\n        <text class=\"cuIcon-add\"></text>\r\n        <text>分享资料</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n\tlet that = null;\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tid: 0,\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tappIsAudit: app.globalData.checkAppIsAudit(),\r\n\t\t\t\tdataList: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthat = this;\r\n\t\t\tthat.id = options.id;\r\n\t\t\tthat.getList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\titemTap(options) {\r\n\t\t\t\tconsole.log(options)\r\n\t\t\t\tlet id = options.currentTarget.dataset.id;\r\n\t\t\t\tlet url = `/pages/practice/material/detail?id=${id}`;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareTap(options) {\r\n\t\t\t\tlet url = `/pages/practice/material/share?id=${that.id}`;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetList() {\r\n\t\t\t\tif(that.appIsAudit){\r\n\t\t\t\t\tthat.isLoad = true;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tapp.globalData.service.getMaterialList(that.id).then(res => {\r\n\t\t\t\t\t\tthat.isLoad = true;\r\n\t\t\t\t\t\tthat.dataList = res.data;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(function(a) {\r\n\t\t\t\t\t\tapp.showToast('获取资料失败');\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n<style src=\"./list.css\"></style>\r\n<style lang=\"scss\" scoped>\r\n\t/* #ifndef H5 */\r\n\tpage {\r\n\t\theight: 100%;\r\n\t\tbackground-color: #f5f7fa;\r\n\t}\r\n\t/* #endif */\r\n\t\r\n\t.material-list-ios {\r\n\t\tbackground: #f7f7fa;\r\n\t\tmin-height: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t.list-content {\r\n\t\tflex: 1;\r\n\t\tpadding: 32rpx 0 120rpx 0;\r\n\t}\r\n\t.scroll-area {\r\n\t\tmin-height: 60vh;\r\n\t}\r\n\t.material-card {\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 24rpx;\r\n\t\tmargin: 32rpx 32rpx 0 32rpx;\r\n\t\tpadding: 40rpx 32rpx 32rpx 32rpx;\r\n\t\tbox-shadow: 0 4rpx 32rpx rgba(0,0,0,0.06);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 24rpx;\r\n\t}\r\n\t.card-main {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: flex-start;\r\n\t}\r\n\t.card-title-row {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\t.card-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #222;\r\n\t}\r\n\t.card-tags {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-end;\r\n\t\tgap: 12rpx;\r\n\t}\r\n\t.price-tag {\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 8rpx 24rpx;\r\n\t}\r\n\t.version-tag {\r\n\t\tfont-size: 22rpx;\r\n\t\tpadding: 4rpx 16rpx;\r\n\t}\r\n\t.card-meta-row {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-top: 24rpx;\r\n\t}\r\n\t.meta-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: baseline;\r\n\t\tgap: 8rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #888;\r\n\t}\r\n\t.center-value {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 100%;\r\n\t}\r\n\t.meta-label {\r\n\t\tmargin-left: 4rpx;\r\n\t}\r\n\t.meta-value {\r\n\t\tcolor: #39b54a;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-left: 8rpx;\r\n\t}\r\n\t.meta-action {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.detail-btn {\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 16rpx 40rpx;\r\n\t\tfont-weight: 500;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\t.float-action-container {\r\n\t\tposition: fixed;\r\n\t\tbottom: 30rpx;\r\n\t\tright: 30rpx;\r\n\t\tz-index: 99;\r\n\t}\r\n\t.share-button {\r\n\t\tbackground: linear-gradient(135deg, #0081ff, #1cbbb4);\r\n\t\tborder-radius: 50rpx;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 129, 255, 0.3);\r\n\t\tpadding: 16rpx 32rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.share-button text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\t.share-button .cuIcon-add {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=1&id=487aa56b&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=1&id=487aa56b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753606627371\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}