{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/桌面/thinker/app/pages/practice/practice.vue?0796", "webpack:///D:/桌面/thinker/app/pages/practice/practice.vue?9d3d", "webpack:///D:/桌面/thinker/app/pages/practice/practice.vue?a0d6", "webpack:///D:/桌面/thinker/app/pages/practice/practice.vue?3c0c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC2D;AACL;AACc;AACA;;;AAGpE;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5SA;AAAA;AAAA;AAAA;AAAq8B,CAAgB,+5BAAG,EAAC,C;;;;;;;;;;;ACAz9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/practice/practice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/practice/practice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./practice.vue?vue&type=template&id=53884e34&\"\nvar renderjs\nimport script from \"./practice.js?vue&type=script&lang=js&\"\nexport * from \"./practice.js?vue&type=script&lang=js&\"\nimport style0 from \"./practice.css?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./practice.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/practice/practice.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=template&id=53884e34&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.showWebView\n    ? _vm.__map(_vm.listData, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.A &&\n          item.is_correct == 0\n            ? item.select_answer.indexOf(\"A\")\n            : null\n        var g1 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.A &&\n          !(item.is_correct == 0) &&\n          item.is_correct == 1\n            ? item.select_answer.indexOf(\"A\") != -1 &&\n              item.correctOptionArr.indexOf(\"A\") != -1\n            : null\n        var g2 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.A &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1)\n            ? item.select_answer.indexOf(\"A\")\n            : null\n        var g3 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.A &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1) &&\n          g2 != -1\n            ? item.select_answer.indexOf(\"A\") != -1 &&\n              item.correctOptionArr.indexOf(\"A\") != -1\n            : null\n        var g4 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.B &&\n          item.is_correct == 0\n            ? item.select_answer.indexOf(\"B\")\n            : null\n        var g5 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.B &&\n          !(item.is_correct == 0) &&\n          item.is_correct == 1\n            ? item.select_answer.indexOf(\"B\") != -1 &&\n              item.correctOptionArr.indexOf(\"B\") != -1\n            : null\n        var g6 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.B &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1)\n            ? item.select_answer.indexOf(\"B\")\n            : null\n        var g7 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.B &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1) &&\n          g6 != -1\n            ? item.select_answer.indexOf(\"B\") != -1 &&\n              item.correctOptionArr.indexOf(\"B\") != -1\n            : null\n        var g8 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.C &&\n          item.is_correct == 0\n            ? item.select_answer.indexOf(\"C\")\n            : null\n        var g9 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.C &&\n          !(item.is_correct == 0) &&\n          item.is_correct == 1\n            ? item.select_answer.indexOf(\"C\") != -1 &&\n              item.correctOptionArr.indexOf(\"C\") != -1\n            : null\n        var g10 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.C &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1)\n            ? item.select_answer.indexOf(\"C\")\n            : null\n        var g11 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.C &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1) &&\n          g10 != -1\n            ? item.select_answer.indexOf(\"C\") != -1 &&\n              item.correctOptionArr.indexOf(\"C\") != -1\n            : null\n        var g12 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.D &&\n          item.is_correct == 0\n            ? item.select_answer.indexOf(\"D\")\n            : null\n        var g13 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.D &&\n          !(item.is_correct == 0) &&\n          item.is_correct == 1\n            ? item.select_answer.indexOf(\"D\") != -1 &&\n              item.correctOptionArr.indexOf(\"D\") != -1\n            : null\n        var g14 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.D &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1)\n            ? item.select_answer.indexOf(\"D\")\n            : null\n        var g15 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.D &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1) &&\n          g14 != -1\n            ? item.select_answer.indexOf(\"D\") != -1 &&\n              item.correctOptionArr.indexOf(\"D\") != -1\n            : null\n        var g16 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.E &&\n          item.is_correct == 0\n            ? item.select_answer.indexOf(\"E\")\n            : null\n        var g17 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.E &&\n          !(item.is_correct == 0) &&\n          item.is_correct == 1\n            ? item.select_answer.indexOf(\"E\") != -1 &&\n              item.correctOptionArr.indexOf(\"E\") != -1\n            : null\n        var g18 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.E &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1)\n            ? item.select_answer.indexOf(\"E\")\n            : null\n        var g19 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.E &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1) &&\n          g18 != -1\n            ? item.select_answer.indexOf(\"E\") != -1 &&\n              item.correctOptionArr.indexOf(\"E\") != -1\n            : null\n        var g20 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.F &&\n          item.is_correct == 0\n            ? item.select_answer.indexOf(\"F\")\n            : null\n        var g21 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.F &&\n          !(item.is_correct == 0) &&\n          item.is_correct == 1\n            ? item.select_answer.indexOf(\"F\") != -1 &&\n              item.correctOptionArr.indexOf(\"F\") != -1\n            : null\n        var g22 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.F &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1)\n            ? item.select_answer.indexOf(\"F\")\n            : null\n        var g23 =\n          !(item.type == 1 || item.type == 3) &&\n          item.type == 2 &&\n          item.F &&\n          !(item.is_correct == 0) &&\n          !(item.is_correct == 1) &&\n          g22 != -1\n            ? item.select_answer.indexOf(\"F\") != -1 &&\n              item.correctOptionArr.indexOf(\"F\") != -1\n            : null\n        var g24 = item.question_images.length\n        var g25 =\n          item.is_correct > 0 &&\n          (item.support_answer ||\n            _vm.selectModelIndex == 1 ||\n            item.is_correct >= 1) &&\n          item.is_correct > 0 &&\n          (item.type == 1 || item.type == 2 || item.type == 3)\n            ? Array.isArray(item.select_answer)\n            : null\n        var g26 =\n          item.is_correct > 0 &&\n          (item.support_answer ||\n            _vm.selectModelIndex == 1 ||\n            item.is_correct >= 1) &&\n          item.is_correct > 0 &&\n          (item.type == 1 || item.type == 2 || item.type == 3) &&\n          g25\n            ? item.select_answer.join(\"\")\n            : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n          g2: g2,\n          g3: g3,\n          g4: g4,\n          g5: g5,\n          g6: g6,\n          g7: g7,\n          g8: g8,\n          g9: g9,\n          g10: g10,\n          g11: g11,\n          g12: g12,\n          g13: g13,\n          g14: g14,\n          g15: g15,\n          g16: g16,\n          g17: g17,\n          g18: g18,\n          g19: g19,\n          g20: g20,\n          g21: g21,\n          g22: g22,\n          g23: g23,\n          g24: g24,\n          g25: g25,\n          g26: g26,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./practice.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753609028466\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}