{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?c8fa", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?97b2", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?97db", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?4118", "uni-app:///pages/responsive-demo/responsive-demo.vue", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?99bd", "webpack:///D:/桌面/thinker/app/pages/responsive-demo/responsive-demo.vue?dbfb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "showDebug", "menuItems", "name", "icon", "cardItems", "title", "desc", "methods", "toggleDebug", "handleCardTap", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,kOAEN;AACP,KAAK;AACL;AACA,aAAa,kOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+EvrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC,YACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,YACA;QAAAC;QAAAC;QAAAH;MAAA,GACA;QAAAE;QAAAC;QAAAH;MAAA,GACA;QAAAE;QAAAC;QAAAH;MAAA,GACA;QAAAE;QAAAC;QAAAH;MAAA,GACA;QAAAE;QAAAC;QAAAH;MAAA,GACA;QAAAE;QAAAC;QAAAH;MAAA;IAEA;EACA;EACAI;IACAC;MACA;IACA;IACAC;MACAC;QACAL;QACAF;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjHA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/responsive-demo/responsive-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/responsive-demo/responsive-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./responsive-demo.vue?vue&type=template&id=4f69b280&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f69b280\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/responsive-demo/responsive-demo.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=template&id=4f69b280&scoped=true&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    responsiveContainer: function () {\n      return import(\n        /* webpackChunkName: \"components/responsive-container/responsive-container\" */ \"@/components/responsive-container/responsive-container.vue\"\n      )\n    },\n    responsiveGrid: function () {\n      return import(\n        /* webpackChunkName: \"components/responsive-grid/responsive-grid\" */ \"@/components/responsive-grid/responsive-grid.vue\"\n      )\n    },\n    responsiveCard: function () {\n      return import(\n        /* webpackChunkName: \"components/responsive-card/responsive-card\" */ \"@/components/responsive-card/responsive-card.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view :class=\"responsiveClasses\">\n    <back :showBackText=\"false\" :showBackIcon=\"false\" :showBackLeft=\"false\" :showHomeIcon=\"false\"\n      customClass=\"bg-gradual-blue text-white\" title=\"响应式演示\"></back>\n    \n    <!-- 调试信息 -->\n    <view class=\"debug-info\" v-if=\"showDebug\">\n      <text class=\"responsive-text-sm\">断点: {{responsive.breakpoint}}</text>\n      <text class=\"responsive-text-sm\">布局: {{responsive.layoutMode}}</text>\n      <text class=\"responsive-text-sm\">列数: {{responsive.gridCols}}</text>\n      <text class=\"responsive-text-sm\">侧边栏: {{responsive.showSidebar ? '显示' : '隐藏'}}</text>\n      <button @tap=\"toggleDebug\" class=\"debug-toggle\">隐藏调试</button>\n    </view>\n    <button v-else @tap=\"toggleDebug\" class=\"debug-toggle\">显示调试</button>\n    \n    <responsive-container>\n      <!-- 侧边栏布局（大屏显示） -->\n      <view class=\"responsive-flex\" v-if=\"responsive.showSidebar\">\n        <!-- 侧边栏 -->\n        <view class=\"responsive-sidebar responsive-padding-base\">\n          <view class=\"sidebar-section\">\n            <text class=\"responsive-text-lg text-bold text-blue\">导航菜单</text>\n            <view class=\"sidebar-menu responsive-margin-base\">\n              <view class=\"sidebar-menu-item\" v-for=\"(item, index) in menuItems\" :key=\"index\">\n                <text :class=\"item.icon\" class=\"text-blue\"></text>\n                <text class=\"responsive-text-base\">{{item.name}}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 主内容区 -->\n        <view class=\"responsive-main responsive-padding-base\">\n          <view class=\"desktop-content\">\n            <text class=\"responsive-text-2xl text-bold margin-bottom\">桌面布局</text>\n            \n            <!-- 卡片网格 -->\n            <responsive-grid :cols=\"{xs:1,sm:2,md:3,lg:4,xl:5,xxl:6}\" :gap=\"20\">\n              <responsive-card \n                v-for=\"(item, index) in cardItems\" :key=\"index\"\n                :clickable=\"true\" \n                @tap=\"handleCardTap(item)\">\n                <view class=\"card-content\">\n                  <text :class=\"item.icon\" class=\"card-icon text-blue\"></text>\n                  <text class=\"card-title responsive-text-base\">{{item.title}}</text>\n                  <text class=\"card-desc responsive-text-sm text-gray\">{{item.desc}}</text>\n                </view>\n              </responsive-card>\n            </responsive-grid>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 移动端布局 -->\n      <view v-else class=\"mobile-content responsive-padding-base\">\n        <text class=\"responsive-text-2xl text-bold margin-bottom\">移动端布局</text>\n        \n        <!-- 响应式列表 -->\n        <view class=\"responsive-list\">\n          <responsive-card \n            v-for=\"(item, index) in cardItems\" :key=\"index\"\n            class=\"responsive-list-item\"\n            :clickable=\"true\" \n            @tap=\"handleCardTap(item)\">\n            <view class=\"list-item-content\">\n              <text :class=\"item.icon\" class=\"list-icon text-blue\"></text>\n              <view class=\"list-info\">\n                <text class=\"list-title responsive-text-base\">{{item.title}}</text>\n                <text class=\"list-desc responsive-text-sm text-gray\">{{item.desc}}</text>\n              </view>\n            </view>\n          </responsive-card>\n        </view>\n      </view>\n    </responsive-container>\n  </view>\n</template>\n\n<script>\nimport responsiveMixin from '@/mixins/responsive.js';\n\nexport default {\n  mixins: [responsiveMixin],\n  data() {\n    return {\n      showDebug: true,\n      menuItems: [\n        { name: '首页', icon: 'cuIcon-home' },\n        { name: '搜索', icon: 'cuIcon-search' },\n        { name: '练习', icon: 'cuIcon-edit' },\n        { name: '我的', icon: 'cuIcon-people' }\n      ],\n      cardItems: [\n        { title: '功能一', desc: '这是功能一的描述', icon: 'cuIcon-apps' },\n        { title: '功能二', desc: '这是功能二的描述', icon: 'cuIcon-settings' },\n        { title: '功能三', desc: '这是功能三的描述', icon: 'cuIcon-message' },\n        { title: '功能四', desc: '这是功能四的描述', icon: 'cuIcon-favor' },\n        { title: '功能五', desc: '这是功能五的描述', icon: 'cuIcon-share' },\n        { title: '功能六', desc: '这是功能六的描述', icon: 'cuIcon-info' }\n      ]\n    };\n  },\n  methods: {\n    toggleDebug() {\n      this.showDebug = !this.showDebug;\n    },\n    handleCardTap(item) {\n      uni.showToast({\n        title: `点击了${item.title}`,\n        icon: 'none'\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.debug-info {\n  position: fixed;\n  top: 100rpx;\n  right: 20rpx;\n  background: rgba(0,0,0,0.8);\n  color: white;\n  padding: 20rpx;\n  border-radius: 10rpx;\n  z-index: 9999;\n  display: flex;\n  flex-direction: column;\n  gap: 10rpx;\n}\n\n.debug-toggle {\n  position: fixed;\n  top: 100rpx;\n  right: 20rpx;\n  z-index: 9999;\n  background: #007aff;\n  color: white;\n  border: none;\n  padding: 10rpx 20rpx;\n  border-radius: 5rpx;\n  font-size: 24rpx;\n}\n\n.sidebar-menu-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n  cursor: pointer;\n}\n\n.sidebar-menu-item text:first-child {\n  margin-right: 15rpx;\n  font-size: 32rpx;\n}\n\n.card-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.card-icon {\n  font-size: 60rpx;\n  margin-bottom: 15rpx;\n}\n\n.card-title {\n  margin-bottom: 10rpx;\n  font-weight: bold;\n}\n\n.list-item-content {\n  display: flex;\n  align-items: center;\n}\n\n.list-icon {\n  font-size: 50rpx;\n  margin-right: 20rpx;\n}\n\n.list-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.list-title {\n  margin-bottom: 8rpx;\n  font-weight: bold;\n}\n\n.margin-bottom {\n  margin-bottom: 30rpx;\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-demo.vue?vue&type=style&index=0&id=4f69b280&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753611117238\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}