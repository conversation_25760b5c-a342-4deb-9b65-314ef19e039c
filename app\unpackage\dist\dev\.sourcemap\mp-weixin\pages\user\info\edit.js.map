{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/user/info/edit.vue?c876", "webpack:///D:/桌面/thinker/app/pages/user/info/edit.vue?a5ba", "webpack:///D:/桌面/thinker/app/pages/user/info/edit.vue?be9a", "webpack:///D:/桌面/thinker/app/pages/user/info/edit.vue?2e41", "uni-app:///pages/user/info/edit.vue", "webpack:///D:/桌面/thinker/app/pages/user/info/edit.vue?6e89", "webpack:///D:/桌面/thinker/app/pages/user/info/edit.vue?729e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLoad", "user", "avatarInputVal", "nicknameInputVal", "onLoad", "that", "methods", "compressImage", "uni", "src", "success", "ctx", "width", "height", "canvasId", "x", "y", "destWidth", "destHeight", "quality", "resolve", "fail", "console", "reject", "avatarInputTap", "content", "res", "title", "compressedPath", "avatar", "app", "icon", "nicknameInputTap", "wxPhoneBindTap", "code", "wx_phone_code", "then", "catch", "submitTap", "nickname", "mask", "complete", "setTimeout", "cancelTap"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACiL;AACjL,gBAAgB,qLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,spBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4C3rB;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACAA;IACAA;IACAA;IACAA;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;YACA;YACA;YACA;;YAEA;YACA;YACA;;YAEA;YACA;;YAEA;YACAC;cACAC;cACAC;YACA;;YAEA;YACAF;;YAEA;YACAA;YACAA;cACA;cACAH;gBACAM;gBACAC;gBACAC;gBACAJ;gBACAC;gBACAI;gBACAC;gBACAC;gBAAA;gBACAT;kBACAU;gBACA;gBACAC;kBACAC;kBACAC;gBACA;cACA;YACA;UACA;UACAF;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAC;MACAhB;QACAiB;QACAf;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAgB;sBAAA;sBAAA;oBAAA;oBACAlB;sBACAmB;oBACA;oBAAA;oBAAA;oBAAA,OAIAtB;kBAAA;oBAAAuB;oBAAA;oBAAA,OAGA,0BACAA;sBACA;sBACA;oBACA;kBAAA;oBAJAF;oBAKAG;oBACAC;sBACAD;oBACA;oBACAxB;oBACAyB;oBACAtB;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAc;oBACAd;oBACAA;sBACAmB;sBACAI;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;MACA1B;IACA;IACA2B;MACA3B;IACA;IACA;IACA4B;MACA;QACA;MACA;MACAzB;QACAE;UACA;YACAwB;YACAC;UACA;UACA,2CACAC;YACA;cACAd;cACAjB;cACAyB;YACA;cACAA;YACA;UACA,GACAO;YACAP;UACA;QACA;MACA;IACA;IACAQ;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAR;kBACAS;gBACA;cAAA;gBAFAb;gBAGArB;gBACAyB;gBACAtB;kBACAmB;kBACAa;kBACAC;oBACAC;sBACAlC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAmC;MACAnC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtNA;AAAA;AAAA;AAAA;AAAs9B,CAAgB,25BAAG,EAAC,C;;;;;;;;;;;ACA1+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/info/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/info/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=9512aa80&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/info/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=9512aa80&\"", "var components\ntry {\n  components = {\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n    adfootbanner: function () {\n      return import(\n        /* webpackChunkName: \"components/adfootbanner/adfootbanner\" */ \"@/components/adfootbanner/adfootbanner.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<back :showBackText=\"false\" customClass=\"bg-gradual-blue text-white\" title=\"编辑资料\"></back>\r\n\t\t<view v-if=\"isLoad\" class=\"content-wrapper\">\r\n\t\t\t<!-- 头像选择区域 -->\r\n\t\t\t<view class=\"avatar-section\">\r\n\t\t\t\t<button class=\"avatar-button\" open-type=\"chooseAvatar\" @chooseavatar=\"avatarInputTap\">\r\n\t\t\t\t\t<view class=\"cu-avatar round xl\" :style=\"'background-image:url('+avatarInputVal+');'\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"avatar-hint\">点击更换头像</view>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 表单区域 -->\r\n\t\t\t<view class=\"form-section\">\r\n\t\t\t\t<view class=\"cu-form-group radius shadow-warp\">\r\n\t\t\t\t\t<view class=\"title\"><text class=\"cuIcon-my text-blue margin-right-xs\"></text>昵称</view>\r\n\t\t\t\t\t<input @input=\"nicknameInputTap\" type=\"nickname\" maxlength=\"20\" placeholder=\"填入要修改的昵称\"\r\n\t\t\t\t\t\t:value=\"nicknameInputVal\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-form-group radius shadow-warp\">\r\n\t\t\t\t\t<view class=\"title\"><text class=\"cuIcon-mobile text-blue margin-right-xs\"></text>手机</view>\r\n\t\t\t\t\t<input v-if=\"user.mobile\" disabled :value=\"user.mobile\" />\r\n\t\t\t\t\t<button v-else open-type=\"getPhoneNumber\" @getphonenumber=\"wxPhoneBindTap\" class=\"cu-btn bg-blue sm radius\">\r\n\t\t\t\t\t\t绑定手机号\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 按钮区域 -->\r\n\t\t\t<view class=\"button-section\">\r\n\t\t\t\t<button @tap=\"submitTap\" class=\"cu-btn bg-gradual-blue lg radius shadow-blur\">保存资料</button>\r\n\t\t\t\t<button @tap=\"cancelTap\" class=\"cu-btn bg-grey lg radius shadow-blur\">取消修改</button>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<adfootbanner></adfootbanner>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 用于图片压缩的canvas，不设置固定宽高 -->\r\n\t\t<canvas canvas-id=\"compressCanvas\" id=\"compressCanvas\" style=\"position: absolute; left: -9999px; top: -9999px;\"></canvas>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tupload,\r\n\t\tpost\r\n\t} from \"@/common/js/http.js\";\r\n\timport { isSet } from \"@/common/js/util.js\";\r\n\tlet app = getApp();\r\n\tlet that = null;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoad: false,\r\n\t\t\t\tuser: {},\r\n\t\t\t\tavatarInputVal: '',\r\n\t\t\t\tnicknameInputVal: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthat = this;\r\n\t\t\tlet userInfo = app.globalData.config.storage.getUserInfoData();\r\n\t\t\tthat.user = userInfo;\r\n\t\t\tthat.isLoad = true;\r\n\t\t\tthat.avatarInputVal = userInfo.avatar;\r\n\t\t\tthat.nicknameInputVal = userInfo.nickname;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 压缩图片方法\r\n\t\t\tcompressImage(path) {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\t\tsrc: path,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tlet width = res.width;\r\n\t\t\t\t\t\t\tlet height = res.height;\r\n\t\t\t\t\t\t\tlet scale = width / height;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 设置最大宽度为800px，保持原比例\r\n\t\t\t\t\t\t\tlet targetWidth = Math.min(width, 800);\r\n\t\t\t\t\t\t\tlet targetHeight = targetWidth / scale;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 创建canvas上下文\r\n\t\t\t\t\t\t\tconst ctx = uni.createCanvasContext('compressCanvas', that);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 设置canvas尺寸与目标图片尺寸一致\r\n\t\t\t\t\t\t\tctx.canvas = {\r\n\t\t\t\t\t\t\t\twidth: targetWidth,\r\n\t\t\t\t\t\t\t\theight: targetHeight\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 清空画布\r\n\t\t\t\t\t\t\tctx.clearRect(0, 0, targetWidth, targetHeight);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 绘制图片到canvas，确保绘制整张图片\r\n\t\t\t\t\t\t\tctx.drawImage(path, 0, 0, targetWidth, targetHeight);\r\n\t\t\t\t\t\t\tctx.draw(false, () => {\r\n\t\t\t\t\t\t\t\t// 将canvas转为图片\r\n\t\t\t\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\t\t\t\tcanvasId: 'compressCanvas',\r\n\t\t\t\t\t\t\t\t\tx: 0,\r\n\t\t\t\t\t\t\t\t\ty: 0,\r\n\t\t\t\t\t\t\t\t\twidth: targetWidth,\r\n\t\t\t\t\t\t\t\t\theight: targetHeight,\r\n\t\t\t\t\t\t\t\t\tdestWidth: targetWidth,\r\n\t\t\t\t\t\t\t\t\tdestHeight: targetHeight,\r\n\t\t\t\t\t\t\t\t\tquality: 0.8, // 压缩质量，范围0-1\r\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\tresolve(res.tempFilePath);\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error('压缩图片失败:', err);\r\n\t\t\t\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}, that);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('获取图片信息失败:', err);\r\n\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tavatarInputTap(options) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\tcontent: '确认使用此头像?',\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t// 压缩图片\r\n\t\t\t\t\t\t\t\tconst compressedPath = await that.compressImage(options.detail.avatarUrl);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 上传压缩后的图片\r\n\t\t\t\t\t\t\t\tlet res = await upload('file',\r\n\t\t\t\t\t\t\t\t\tcompressedPath, {\r\n\t\t\t\t\t\t\t\t\t\t'app': 'learnAppTemp',\r\n\t\t\t\t\t\t\t\t\t\t'sign': app.globalData.getTimestamp()\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tlet avatar = res.data;\r\n\t\t\t\t\t\t\t\tapp.globalData.service.updateUser({\r\n\t\t\t\t\t\t\t\t\tavatar: avatar\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthat.user.avatar = avatar;\r\n\t\t\t\t\t\t\t\tapp.globalData.config.storage.setUserInfoData(that.user);\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\tconsole.error('头像处理失败:', error);\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '头像处理失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tthat.avatarInputVal = options.detail.avatarUrl;\r\n\t\t\t},\r\n\t\t\tnicknameInputTap(options) {\r\n\t\t\t\tthat.nicknameInputVal = options.detail.value;\r\n\t\t\t},\r\n\t\t\t// 微信手机号快捷绑定\r\n\t\t\twxPhoneBindTap(options) {\r\n\t\t\t\tif (!isSet(options.detail.code)) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\tcode: res.code,\r\n\t\t\t\t\t\t\twx_phone_code: options.detail.code\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tpost('user/bindWxMobile', data)\r\n\t\t\t\t\t\t\t.then((t) => {\r\n\t\t\t\t\t\t\t\tif (t.code == 1) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(t);\r\n\t\t\t\t\t\t\t\t\tthat.user.mobile = t.data.mobile;\r\n\t\t\t\t\t\t\t\t\tapp.globalData.config.storage.setUserInfoData(that.user);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tapp.showToast(t.message);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch((t) => {\r\n\t\t\t\t\t\t\t\tapp.showToast('微信手机号绑定异常');\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync submitTap(options) {\r\n\t\t\t\tlet res = await app.globalData.service.updateUser({\r\n\t\t\t\t\tnickname: that.nicknameInputVal\r\n\t\t\t\t});\r\n\t\t\t\tthat.user.nickname = that.nicknameInputVal;\r\n\t\t\t\tapp.globalData.config.storage.setUserInfoData(that.user);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: res.message,\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t\tcomplete: function() {\r\n\t\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t\tuni.navigateBack();\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcancelTap() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\tbutton:after {\r\n\t\tborder: none;\r\n\t}\r\n\t\r\n\t.content-wrapper {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\t\r\n\t.avatar-section {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 40rpx 0;\r\n\t\tbackground: linear-gradient(45deg, #0081ff, #1cbbb4);\r\n\t\tborder-radius: 0 0 50rpx 50rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.avatar-button {\r\n\t\tbackground: transparent;\r\n\t\tpadding: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.cu-avatar.xl {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tborder: 6rpx solid rgba(255, 255, 255, 0.7);\r\n\t\tbox-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\t\r\n\t.avatar-hint {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\t\r\n\t.form-section {\r\n\t\tmargin: 30rpx 0 60rpx;\r\n\t}\r\n\t\r\n\t.cu-form-group {\r\n\t\tbackground-color: #ffffff;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tbox-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\t\r\n\t.cu-form-group .title {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t\r\n\t.button-section {\r\n\t\tpadding: 20rpx 0 40rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tgap: 30rpx;\r\n\t}\r\n\t\r\n\t.cu-btn {\r\n\t\tmargin-bottom: 0;\r\n\t\theight: 90rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tletter-spacing: 2rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tmin-width: 220rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t\r\n\t.cu-btn.bg-gradual-blue,\r\n\t.cu-btn.bg-grey {\r\n\t\tbox-shadow: 0 10rpx 20rpx rgba(0, 129, 255, 0.2);\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753606626336\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}