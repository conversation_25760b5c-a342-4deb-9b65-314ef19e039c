{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/pages/webview/webview.vue?102e", "webpack:///D:/桌面/thinker/app/pages/webview/webview.vue?bdc1", "webpack:///D:/桌面/thinker/app/pages/webview/webview.vue?d51b", "uni-app:///pages/webview/webview.vue", "webpack:///D:/桌面/thinker/app/pages/webview/webview.vue?8f2a", "webpack:///D:/桌面/thinker/app/pages/webview/webview.vue?b1b9", "uni-app:///main.js", null], "names": ["components", "HybridView", "data", "webUrl", "forceMode", "currentMode", "screenSize", "pageName", "urlParams", "onLoad", "hybridUtils", "computed", "modeDescription", "methods", "handleModeChange", "console", "handleWebMessage", "handleWebLoad", "uni", "title", "icon", "handleWebError", "switchMode", "goToPage", "url", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,aAAa,gKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,ypBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgE/qB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAA;IACAC;EACA;EAEAC;IACA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;;IAEA;IACA;MACA;IACA;;IAEA;IACAC;EACA;EAEAC;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACAC;IACA;IAEA;IACAC;MACAD;MACA;MACAL;IACA;IAEA;IACAO;MACAF;MACAG;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAN;MACAG;QACAC;QACAC;MACA;IACA;IAEA;IACAE;MACA;MACAJ;QACAC;QACAC;MACA;IACA;IAEA;IACAG;MACAL;QACAM;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClKA;AAAA;AAAA;AAAA;AAA49B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh/B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF", "file": "pages/webview/webview.js", "sourcesContent": ["export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=template&id=baadaa0c&scoped=true&\"", "var components\ntry {\n  components = {\n    hybridView: function () {\n      return import(\n        /* webpackChunkName: \"components/hybrid-view/hybrid-view\" */ \"@/components/hybrid-view/hybrid-view.vue\"\n      )\n    },\n    back: function () {\n      return import(\n        /* webpackChunkName: \"components/back/back\" */ \"@/components/back/back.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=script&lang=js&\"", "<template>\n  <hybrid-view\n    :web-url=\"webUrl\"\n    :force-mode=\"forceMode\"\n    :debug-mode=\"true\"\n    :url-params=\"urlParams\"\n    @modeChange=\"handleModeChange\"\n    @webMessage=\"handleWebMessage\"\n    @webLoad=\"handleWebLoad\"\n    @webError=\"handleWebError\">\n\n    <!-- 原生小程序内容 -->\n    <view class=\"native-content\">\n      <back :showBackText=\"false\" :showBackIcon=\"true\" :showBackLeft=\"true\" :showHomeIcon=\"false\"\n        customClass=\"bg-gradual-blue text-white\" title=\"混合模式\"></back>\n\n      <view class=\"content-wrapper\">\n        <view class=\"mode-info\">\n          <text class=\"mode-title\">当前模式：{{ currentMode }}</text>\n          <text class=\"mode-desc\">{{ modeDescription }}</text>\n        </view>\n\n        <view class=\"control-panel\">\n          <button class=\"cu-btn bg-blue margin-sm\" @tap=\"switchMode('auto')\">自动模式</button>\n          <button class=\"cu-btn bg-green margin-sm\" @tap=\"switchMode('web')\">强制Web端</button>\n          <button class=\"cu-btn bg-orange margin-sm\" @tap=\"switchMode('native')\">强制原生</button>\n        </view>\n\n        <view class=\"info-panel\">\n          <view class=\"info-item\">\n            <text class=\"info-label\">Web URL:</text>\n            <text class=\"info-value\">{{ webUrl }}</text>\n          </view>\n          <view class=\"info-item\">\n            <text class=\"info-label\">屏幕尺寸:</text>\n            <text class=\"info-value\">{{ screenSize }}</text>\n          </view>\n        </view>\n\n        <!-- 原生小程序的其他内容可以放在这里 -->\n        <view class=\"native-features\">\n          <text class=\"feature-title\">原生小程序功能</text>\n          <view class=\"feature-list\">\n            <view class=\"feature-item\" @tap=\"goToPage('/pages/index/index')\">\n              <text class=\"cuIcon-home\"></text>\n              <text>首页</text>\n            </view>\n            <view class=\"feature-item\" @tap=\"goToPage('/pages/search/search')\">\n              <text class=\"cuIcon-search\"></text>\n              <text>搜索</text>\n            </view>\n            <view class=\"feature-item\" @tap=\"goToPage('/pages/user/user')\">\n              <text class=\"cuIcon-people\"></text>\n              <text>我的</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </hybrid-view>\n</template>\n\n<script>\nimport HybridView from '@/components/hybrid-view/hybrid-view.vue'\nimport hybridUtils from '@/common/js/hybrid-utils.js'\n\nexport default {\n  components: {\n    HybridView\n  },\n\n  data() {\n    return {\n      webUrl: '',\n      forceMode: 'auto', // 'auto', 'web', 'native'\n      currentMode: '自动',\n      screenSize: '',\n      pageName: 'index', // 默认页面\n      urlParams: {}\n    }\n  },\n\n  onLoad(options) {\n    // 从URL参数获取配置\n    if (options.webUrl) {\n      this.webUrl = decodeURIComponent(options.webUrl)\n    }\n    if (options.pageName) {\n      this.pageName = options.pageName\n    }\n    if (options.forceMode) {\n      this.forceMode = options.forceMode\n    }\n\n    // 如果没有提供webUrl，则根据pageName生成\n    if (!this.webUrl && this.pageName) {\n      this.webUrl = hybridUtils.getPageUrl(this.pageName, this.urlParams)\n    }\n\n    // 初始化混合工具\n    hybridUtils.init()\n  },\n\n  computed: {\n    modeDescription() {\n      switch(this.forceMode) {\n        case 'web': return '强制显示Web端界面'\n        case 'native': return '强制显示原生小程序界面'\n        default: return '根据屏幕大小自动切换'\n      }\n    }\n  },\n\n  methods: {\n    // 处理模式变化\n    handleModeChange(data) {\n      this.currentMode = data.mode === 'web' ? 'Web端' : '原生'\n      this.screenSize = `${data.screenInfo.windowWidth}x${data.screenInfo.windowHeight}`\n      console.log('模式切换:', data)\n    },\n\n    // 处理web端消息\n    handleWebMessage(data) {\n      console.log('收到Web端消息:', data)\n      // 使用混合工具处理消息\n      hybridUtils.handleWebMessage(data)\n    },\n\n    // web端加载完成\n    handleWebLoad() {\n      console.log('Web端加载完成')\n      uni.showToast({\n        title: 'Web端加载成功',\n        icon: 'success'\n      })\n    },\n\n    // web端加载错误\n    handleWebError(error) {\n      console.error('Web端加载错误:', error)\n      uni.showToast({\n        title: 'Web端加载失败',\n        icon: 'none'\n      })\n    },\n\n    // 切换模式\n    switchMode(mode) {\n      this.forceMode = mode\n      uni.showToast({\n        title: `切换到${this.modeDescription}`,\n        icon: 'none'\n      })\n    },\n\n    // 跳转页面\n    goToPage(url) {\n      uni.navigateTo({\n        url: url\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.native-content {\n  width: 100%;\n  height: 100vh;\n  background: #f8f9fa;\n}\n\n.content-wrapper {\n  padding: 20rpx;\n}\n\n.mode-info {\n  background: white;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.mode-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 10rpx;\n}\n\n.mode-desc {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.control-panel {\n  background: white;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n  text-align: center;\n}\n\n.info-panel {\n  background: white;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.info-item {\n  margin-bottom: 20rpx;\n}\n\n.info-label {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n  margin-bottom: 5rpx;\n}\n\n.info-value {\n  font-size: 26rpx;\n  color: #333;\n  word-break: break-all;\n  display: block;\n}\n\n.native-features {\n  background: white;\n  padding: 30rpx;\n  border-radius: 20rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.feature-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  display: block;\n  margin-bottom: 20rpx;\n}\n\n.feature-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20rpx;\n}\n\n.feature-item {\n  flex: 1;\n  min-width: 200rpx;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 10rpx;\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10rpx;\n}\n\n.feature-item text:first-child {\n  font-size: 40rpx;\n  color: #007aff;\n}\n\n.feature-item text:last-child {\n  font-size: 24rpx;\n  color: #333;\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=style&index=0&id=baadaa0c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=style&index=0&id=baadaa0c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753607176965\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/webview/webview.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./webview.vue?vue&type=template&id=baadaa0c&scoped=true&\"\nvar renderjs\nimport script from \"./webview.vue?vue&type=script&lang=js&\"\nexport * from \"./webview.vue?vue&type=script&lang=js&\"\nimport style0 from \"./webview.vue?vue&type=style&index=0&id=baadaa0c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"baadaa0c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/webview/webview.vue\"\nexport default component.exports"], "sourceRoot": ""}