<view class="hybrid-container data-v-17dfbb32"><block wx:if="{{showWebView}}"><web-view class="web-view data-v-17dfbb32" src="{{computedWebUrl}}" data-event-opts="{{[['message',[['handleWebMessage',['$event']]]],['load',[['handleWebLoad',['$event']]]],['error',[['handleWebError',['$event']]]]]}}" bindmessage="__e" bindload="__e" binderror="__e"></web-view></block><block wx:else><view class="native-view data-v-17dfbb32"><slot></slot></view></block><block wx:if="{{showDebugInfo}}"><view class="debug-info data-v-17dfbb32"><text class="data-v-17dfbb32">{{"屏幕: "+screenInfo.windowWidth+"x"+screenInfo.windowHeight}}</text><text class="data-v-17dfbb32">{{"模式: "+(showWebView?'Web端':'原生')}}</text><text class="data-v-17dfbb32">{{"比例: "+$root.g0}}</text></view></block></view>