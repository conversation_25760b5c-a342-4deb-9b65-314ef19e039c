
.responsive-card.data-v-ab6b2dd0 {
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}
.shadow-none.data-v-ab6b2dd0 {
  box-shadow: none;
}
.shadow-light.data-v-ab6b2dd0 {
  box-shadow: 0 1rpx 3rpx rgba(0,0,0,0.1);
}
.shadow-normal.data-v-ab6b2dd0 {
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.shadow-heavy.data-v-ab6b2dd0 {
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
}
.card-hover.data-v-ab6b2dd0:hover {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.15);
}
.card-clickable.data-v-ab6b2dd0 {
  cursor: pointer;
}
.card-clickable.data-v-ab6b2dd0:active {
  -webkit-transform: translateY(1rpx);
          transform: translateY(1rpx);
}

