
.responsive-grid.data-v-3f4970f8 {
  width: 100%;
}
.grid-align-start.data-v-3f4970f8 {
  align-items: flex-start;
}
.grid-align-center.data-v-3f4970f8 {
  align-items: center;
}
.grid-align-end.data-v-3f4970f8 {
  align-items: flex-end;
}
.grid-align-stretch.data-v-3f4970f8 {
  align-items: stretch;
}
.grid-justify-start.data-v-3f4970f8 {
  justify-content: flex-start;
}
.grid-justify-center.data-v-3f4970f8 {
  justify-content: center;
}
.grid-justify-end.data-v-3f4970f8 {
  justify-content: flex-end;
}
.grid-justify-between.data-v-3f4970f8 {
  justify-content: space-between;
}
.grid-justify-around.data-v-3f4970f8 {
  justify-content: space-around;
}

