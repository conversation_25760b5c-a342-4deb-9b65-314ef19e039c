<view class="demo-container data-v-ea454ca0"><back vue-id="5e634554-1" showBackText="{{false}}" showBackIcon="{{true}}" showBackLeft="{{true}}" showHomeIcon="{{false}}" customClass="bg-gradual-blue text-white" title="混合模式演示" class="data-v-ea454ca0" bind:__l="__l"></back><view class="demo-content data-v-ea454ca0"><view class="mode-selector data-v-ea454ca0"><view class="selector-title data-v-ea454ca0">选择显示模式</view><view class="selector-buttons data-v-ea454ca0"><button data-event-opts="{{[['tap',[['switchMode',['auto']]]]]}}" class="{{['cu-btn','margin-sm','data-v-ea454ca0',currentMode==='auto'?'bg-blue':'line-blue']}}" bindtap="__e">自动模式</button><button data-event-opts="{{[['tap',[['switchMode',['web']]]]]}}" class="{{['cu-btn','margin-sm','data-v-ea454ca0',currentMode==='web'?'bg-green':'line-green']}}" bindtap="__e">Web端</button><button data-event-opts="{{[['tap',[['switchMode',['native']]]]]}}" class="{{['cu-btn','margin-sm','data-v-ea454ca0',currentMode==='native'?'bg-orange':'line-orange']}}" bindtap="__e">原生端</button></view></view><view class="page-selector data-v-ea454ca0"><view class="selector-title data-v-ea454ca0">选择要演示的页面</view><view class="page-list data-v-ea454ca0"><block wx:for="{{availablePages}}" wx:for-item="page" wx:for-index="key" wx:key="key"><view data-event-opts="{{[['tap',[['openPage',[key]]]]]}}" class="page-item data-v-ea454ca0" bindtap="__e"><view class="page-info data-v-ea454ca0"><text class="page-name data-v-ea454ca0">{{page.title}}</text><text class="page-desc data-v-ea454ca0">{{page.enableHybrid?'支持混合模式':'仅原生模式'}}</text></view><text class="cuIcon-right text-gray data-v-ea454ca0"></text></view></block></view></view><view class="status-panel data-v-ea454ca0"><view class="status-title data-v-ea454ca0">当前状态</view><view class="status-info data-v-ea454ca0"><view class="status-item data-v-ea454ca0"><text class="status-label data-v-ea454ca0">屏幕尺寸:</text><text class="status-value data-v-ea454ca0">{{screenInfo.windowWidth+"x"+screenInfo.windowHeight}}</text></view><view class="status-item data-v-ea454ca0"><text class="status-label data-v-ea454ca0">宽高比:</text><text class="status-value data-v-ea454ca0">{{aspectRatio}}</text></view><view class="status-item data-v-ea454ca0"><text class="status-label data-v-ea454ca0">推荐模式:</text><text class="status-value data-v-ea454ca0">{{recommendedMode}}</text></view><view class="status-item data-v-ea454ca0"><text class="status-label data-v-ea454ca0">当前模式:</text><text class="status-value data-v-ea454ca0">{{currentMode}}</text></view></view></view><view class="config-panel data-v-ea454ca0"><view class="config-title data-v-ea454ca0">配置信息</view><view class="config-info data-v-ea454ca0"><view class="config-item data-v-ea454ca0"><text class="config-label data-v-ea454ca0">大屏阈值:</text><text class="config-value data-v-ea454ca0">{{config.screen.largeScreenWidth+"px"}}</text></view><view class="config-item data-v-ea454ca0"><text class="config-label data-v-ea454ca0">宽高比阈值:</text><text class="config-value data-v-ea454ca0">{{config.screen.aspectRatioThreshold}}</text></view><view class="config-item data-v-ea454ca0"><text class="config-label data-v-ea454ca0">Web端URL:</text><text class="config-value data-v-ea454ca0">{{config.web.currentUrl}}</text></view></view></view></view></view>