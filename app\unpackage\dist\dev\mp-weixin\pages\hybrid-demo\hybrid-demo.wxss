
.demo-container.data-v-ea454ca0 {
  background: #f8f9fa;
  min-height: 100vh;
}
.demo-content.data-v-ea454ca0 {
  padding: 20rpx;
}
.mode-selector.data-v-ea454ca0,
.page-selector.data-v-ea454ca0,
.status-panel.data-v-ea454ca0,
.config-panel.data-v-ea454ca0 {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.selector-title.data-v-ea454ca0,
.status-title.data-v-ea454ca0,
.config-title.data-v-ea454ca0 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.selector-buttons.data-v-ea454ca0 {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}
.page-list.data-v-ea454ca0 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.page-item.data-v-ea454ca0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}
.page-info.data-v-ea454ca0 {
  flex: 1;
}
.page-name.data-v-ea454ca0 {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}
.page-desc.data-v-ea454ca0 {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.status-info.data-v-ea454ca0,
.config-info.data-v-ea454ca0 {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.status-item.data-v-ea454ca0,
.config-item.data-v-ea454ca0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.status-label.data-v-ea454ca0,
.config-label.data-v-ea454ca0 {
  font-size: 28rpx;
  color: #666;
}
.status-value.data-v-ea454ca0,
.config-value.data-v-ea454ca0 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

