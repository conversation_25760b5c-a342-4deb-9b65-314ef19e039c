<view class="{{[responsiveClasses]}}"><back vue-id="8dd740cc-1" showBackText="{{false}}" showBackIcon="{{false}}" showBackLeft="{{false}}" showHomeIcon="{{false}}" customClass="bg-gradual-blue text-white" title="首页" bind:__l="__l"></back><view hidden="{{!(!isLoading)}}" class="responsive-container"><block wx:if="{{responsive.showSidebar}}"><view class="responsive-flex"><view class="responsive-sidebar responsive-padding-base"><view class="sidebar-section"><text class="responsive-text-lg text-bold text-blue">快速导航</text><view class="sidebar-menu"><view data-event-opts="{{[['tap',[['goTo',['city/city?first_visit=1']]]]]}}" class="sidebar-menu-item" bindtap="__e"><text class="cuIcon-location text-blue"></text><text class="responsive-text-base">{{currentCity.name||'选择城市'}}</text></view><view data-event-opts="{{[['tap',[['goTo',['exam/exam']]]]]}}" class="sidebar-menu-item" bindtap="__e"><text class="cuIcon-group text-blue"></text><text class="responsive-text-base">{{currentExam.name||'选择考试'}}</text></view><view data-event-opts="{{[['tap',[['goTo',['profession/profession']]]]]}}" class="sidebar-menu-item" bindtap="__e"><text class="cuIcon-read text-blue"></text><text class="responsive-text-base">{{currentProfession.name||'选择专业'}}</text></view></view></view></view><view class="responsive-main responsive-padding-base"><view class="desktop-layout"><view class="swiper-container"><swiper class="responsive-swiper" indicator-dots="{{true}}" circular="{{true}}" autoplay="{{true}}" interval="5000" duration="500"><block wx:for="{{images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['tap',[['goTo',['$0'],[[['images','',index,'target_url']]]]]]]}}" bindtap="__e"><image class="responsive-swiper-image" src="{{item.img_url}}" mode="aspectFill"></image></swiper-item></block></swiper></view><view class="menu-grid-container responsive-margin-base"><view class="responsive-grid"><block wx:for="{{menuList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="responsive-grid-item"><view data-event-opts="{{[['tap',[['goTo',['$0'],[[['menuList','',index,'page_uri']]]]]]]}}" class="menu-card responsive-card" bindtap="__e"><image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image><text class="menu-text responsive-text-base">{{item.name}}</text></view></view></block></view></view></view></view></view></block><block wx:else><view class="mobile-layout"><view class="cu-bar bg-white search"><view data-event-opts="{{[['tap',[['goTo',['city/city?first_visit=1']]]]]}}" class="action" style="font-size:27rpx;max-width:33.3333%;" bindtap="__e"><text class="cuIcon-location text-blue" style="margin:0;"></text><text class="text-blue text-cut">{{currentCity.name}}</text></view><view data-event-opts="{{[['tap',[['goTo',['exam/exam']]]]]}}" class="action" style="font-size:27rpx;max-width:33.3333%;" bindtap="__e"><text class="cuIcon-group text-blue" style="margin-right:6rpx;"></text><text class="text-blue text-cut">{{currentExam.name}}</text></view><view data-event-opts="{{[['tap',[['goTo',['profession/profession']]]]]}}" class="action" style="font-size:27rpx;max-width:33.3333%;" bindtap="__e"><text class="cuIcon-read text-blue" style="margin-right:6rpx;"></text><text class="text-blue text-cut">{{currentProfession.name||'选择专业'}}</text></view></view><swiper class="swiper screen-swiper square-dot margin-top-xs" style="min-height:0;" indicator-dots="{{true}}" circular="{{true}}" autoplay="{{true}}" interval="5000" duration="500"><block wx:for="{{images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['tap',[['goTo',['$0'],[[['images','',index,'target_url']]]]]]]}}" bindtap="__e"><image class="swiper-image" src="{{item.img_url}}"></image></swiper-item></block></swiper><block wx:if="{{!appIsAudit&&showIndexShareName&&(appPlatform==20||appPlatform==21)}}"><view class="cu-bar bg-white margin-top-xs no-border" style="font-size:50rpx;display:none;"><view class="action" style="font-size:48rpx;"><text class="cuIcon-notificationfill text-blue" style="font-size:50rpx;"></text><text class="text-blue text-df">{{showIndexShareName}}</text></view><view class="action"><button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享<text class="cuIcon-share" style="margin-left:6rpx;"></text></button></view></view></block><block wx:if="{{examTime.show}}"><view class="exam-countdown responsive-margin-sm"><view class="countdown-content responsive-card responsive-padding-base"><view class="countdown-header"><text class="cuIcon-time text-blue"></text><text class="countdown-title responsive-text-lg">考试倒计时</text></view><view class="countdown-info"><view class="countdown-date responsive-text-base">{{"考试日期："+examTime.exam_date}}</view><view class="countdown-timer"><text class="countdown-unit responsive-text-sm">还剩</text><text class="countdown-days responsive-text-xl text-bold">{{examTime.exam_remain}}</text><text class="countdown-unit responsive-text-sm">天</text></view></view></view></view></block><view class="cu-bar bg-white margin-top-xs"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">学习中心</text><text class="text-ABC text-blue">Study</text></view><view class="action"><button class="cu-btn margin-tb-sm bg-blue shadow" open-type="share">分享<text class="cuIcon-share" style="margin-left:6rpx;"></text></button></view></view><view class="responsive-list bg-white radius shadow-warp responsive-padding-base"><block wx:for="{{menuList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="responsive-list-item"><view data-event-opts="{{[['tap',[['goTo',['$0'],[[['menuList','',index,'page_uri']]]]]]]}}" class="menu-card responsive-card" bindtap="__e"><image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image><text class="menu-text responsive-text-base">{{item.name}}</text></view></view></block></view></view></block><block wx:if="{{!appIsAudit}}"><view class="article-section"><view class="cu-bar bg-white margin-top-xs" style="margin-bottom:3rpx;"><view class="action sub-title"><text class="text-xl text-bold text-blue text-shadow">最新资讯</text><text class="text-ABC text-blue">News</text></view><view data-event-opts="{{[['tap',[['goTo',['./article/list']]]]]}}" class="action" bindtap="__e"><button class="cu-btn bg-blue margin-tb-sm shadow" data-target="Modal">更多<text class="cuIcon-moreandroid" style="margin-left:5rpx;"></text></button></view></view><view class="responsive-list"><block wx:for="{{articleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{!appIsAudit||appPlatform!=20&&appPlatform!=21}}"><view class="responsive-list-item"><view data-event-opts="{{[['tap',[['goTo',['./article/detail?id='+item.id]]]]]}}" class="article-card responsive-card" bindtap="__e"><view class="article-content"><view class="article-image"><image class="radius" src="{{item.thumb}}" mode="aspectFit"></image></view><view class="article-info"><view class="article-title responsive-text-base text-black">{{item.title}}</view><view class="article-meta"><view class="text-gray responsive-text-sm">{{item.cate_name+" · "+item.create_date}}</view><view class="text-gray responsive-text-sm">{{item.page_view+" 阅读"}}</view></view></view></view></view></view></block></block></view></view></block></view></view>