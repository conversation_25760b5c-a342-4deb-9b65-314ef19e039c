.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}


/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}
.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}
.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}
.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-date {
	font-size: 26rpx;
	color: #555;
}
.countdown-timer {
	display: flex;
	align-items: center;
}
.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}
.countdown-unit {
	font-size: 28rpx;
	color: #555;
}


/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}
.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.modern-grid-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	opacity: 0.8;
}
.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}
.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}


/* 响应式轮播图 */
.responsive-swiper {
	width: 100%;
	height: 300rpx;
	border-radius: 10rpx;
}
.large-screen .responsive-swiper {
	height: 400rpx;
}
.responsive-swiper-image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
}


/* 侧边栏样式 */
.sidebar-section {
	margin-bottom: 40rpx;
}
.sidebar-menu {
	margin-top: 20rpx;
}
.sidebar-menu-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}
.sidebar-menu-item:hover {
	background-color: #f8f9fa;
}
.sidebar-menu-item text:first-child {
	margin-right: 15rpx;
	font-size: 32rpx;
}


/* 功能菜单卡片 */
.menu-card {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
	cursor: pointer;
	border-radius: 12rpx;
}
.menu-card:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
	box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.15);
}
.menu-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 15rpx;
}
.large-screen .menu-icon {
	width: 100rpx;
	height: 100rpx;
	margin-bottom: 20rpx;
}
.menu-text {
	color: #333;
	font-weight: 500;
}


/* 文章卡片样式 */
.article-card {
	padding: 20rpx;
	transition: all 0.3s ease;
	cursor: pointer;
	border-radius: 12rpx;
}
.article-card:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.1);
}
.article-content {
	display: flex;
	align-items: flex-start;
}
.article-image {
	flex-shrink: 0;
	margin-right: 20rpx;
}
.article-image image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
}
.large-screen .article-image image {
	width: 150rpx;
	height: 150rpx;
}
.article-info {
	flex: 1;
	min-width: 0;
}
.article-title {
	margin-bottom: 15rpx;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
.article-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}


/* 桌面布局特殊样式 */
.desktop-layout {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
.desktop-layout .swiper-container {
	margin-bottom: 20rpx;
}
.desktop-layout .menu-grid-container {
	margin-bottom: 20rpx;
}


/* 移动端布局 */
.mobile-layout {
	padding: 0 15rpx;
}

