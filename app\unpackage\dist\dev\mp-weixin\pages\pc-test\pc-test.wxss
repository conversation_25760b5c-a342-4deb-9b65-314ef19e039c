
.test-container.data-v-1bd04540 {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}
.test-header.data-v-1bd04540 {
	text-align: center;
	padding: 40rpx 0;
	background-color: #0081ff;
	color: white;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
}
.test-title.data-v-1bd04540 {
	font-size: 36rpx;
	font-weight: bold;
}
.test-info.data-v-1bd04540 {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}
.info-item.data-v-1bd04540 {
	display: flex;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}
.info-item.data-v-1bd04540:last-child {
	border-bottom: none;
}
.info-label.data-v-1bd04540 {
	font-size: 28rpx;
	color: #666;
}
.info-value.data-v-1bd04540 {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}
.test-grid.data-v-1bd04540 {
	display: flex;
	flex-wrap: wrap;
	background-color: white;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
}
.grid-item.data-v-1bd04540 {
	padding: 10rpx;
	box-sizing: border-box;
}
.grid-content.data-v-1bd04540 {
	background-color: #f0f8ff;
	border: 2rpx solid #0081ff;
	border-radius: 8rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	transition: all 0.3s ease;
}
.grid-content.data-v-1bd04540:hover {
	background-color: #0081ff;
	color: white;
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
}
.test-buttons.data-v-1bd04540 {
	text-align: center;
}
.test-btn.data-v-1bd04540 {
	background-color: #0081ff;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* PC端适配 */
@media screen and (min-width: 768px) {
.test-container.data-v-1bd04540 {
		max-width: 1200px;
		margin: 0 auto;
		padding: 40px 20px;
}
.test-title.data-v-1bd04540 {
		font-size: 24px;
}
.info-label.data-v-1bd04540, .info-value.data-v-1bd04540 {
		font-size: 16px;
}
.test-btn.data-v-1bd04540 {
		font-size: 16px;
		padding: 12px 24px;
}
.test-btn.data-v-1bd04540:hover {
		background-color: #0066cc;
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}
}

