<view class="{{['app-container',[(isDarkMode)?'dark-mode':''],responsiveClasses]}}"><back vue-id="1d807640-1" showBackText="{{false}}" customClass="{{isDarkMode?'bg-dark text-white':'bg-gradual-blue text-white'}}" title="{{title}}" data-event-opts="{{[['^beforeBack',[['handleBeforeBack']]]]}}" bind:beforeBack="__e" bind:__l="__l"></back><view class="responsive-container"><block wx:if="{{responsive.showSidebar}}"><view class="responsive-flex"><view class="responsive-sidebar responsive-padding-base"><view class="practice-sidebar"><text class="responsive-text-lg text-bold">练习模式</text><view class="practice-modes responsive-margin-base"><block wx:for="{{selectModel}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['mode-item',index==selectModelIndex?'active':'']}}" data-id="{{index}}" data-event-opts="{{[['tap',[['selectModelTap',['$event']]]]]}}" bindtap="__e"><text class="responsive-text-base">{{item}}</text></view></block></view><view class="practice-progress responsive-margin-base"><text class="responsive-text-base text-bold">进度</text><view class="progress-info"><text class="responsive-text-sm">{{"第"+((currentPage-1)*limit+swiperCurrent+1)+"道题"}}</text><text class="responsive-text-sm">{{"共"+questionCount+"道题"}}</text></view></view></view></view><view class="questionAsk-layout margin-top-xs" style="{{(cssData.questionTypeTagAndTitleCss)}}"><view class="cu-capsule radius margin"><view class="cu-tag" style="{{(cssData.questionTypeTagTypeCss)}}">{{''+item.type_name+''}}</view><view class="cu-tag line-blue" style="{{(cssData.questionTypeTagCss)}}">{{'共'+questionCount+"道题，第"+((currentPage-1)*limit+swiperCurrent+1)+'道题'}}</view></view></view><block wx:if="{{backgroundData[item.background_id]}}"><view><view class="questionAsk-layout margin-top-xs"><view class="margin-top margin-left margin-right margin-bottom-xs" style="{{(cssData.questionContentCss)}}"><rich-text nodes="{{backgroundData[item.background_id]}}"></rich-text></view></view></view></block><view class="questionAsk-layout question-content" style="padding:0rpx 30rpx 30rpx 30rpx;"><view class="{{[backgroundData[item.background_id]?'margin-top':'']}}"><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.questionAsk}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.questionAsk']]]]]}}" bindlongpress="__e"></rich-text></view><block wx:if="{{!item.support_answer}}"><view class="nosupport">本题暂不支持作答</view></block></view></view></block><block wx:if="{{item.type==1||item.type==3}}"><view class="options-layout margin-top-xs" style="{{(cssData.questionContentCss)}}"><block wx:if="{{item.A}}"><view class="{{[(item.select_answer=='A'?item.is_correct==1?'layout-result-correct':'layout-result-error':item.correctOption=='A'?item.select_answer&&item.is_correct==2?'layout-result-correct':'':'')+' layout-result']}}" data-answer="A" data-event-opts="{{[['tap',[['singleSelectTap',['$event']]]]]}}" bindtap="__e"><text>A</text><rich-text nodes="{{item.A}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.A']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.B}}"><view class="{{[(item.select_answer=='B'?item.is_correct==1?'layout-result-correct':'layout-result-error':item.correctOption=='B'?item.select_answer&&item.is_correct==2?'layout-result-correct':'':'')+' layout-result']}}" data-answer="B" data-event-opts="{{[['tap',[['singleSelectTap',['$event']]]]]}}" bindtap="__e"><text>B</text><rich-text nodes="{{item.B}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.B']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.C}}"><view class="{{[(item.select_answer=='C'?item.is_correct==1?'layout-result-correct':'layout-result-error':item.correctOption=='C'?item.select_answer&&item.is_correct==2?'layout-result-correct':'':'')+' layout-result']}}" data-answer="C" data-event-opts="{{[['tap',[['singleSelectTap',['$event']]]]]}}" bindtap="__e"><text>C</text><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.C}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.C']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.D}}"><view class="{{[(item.select_answer=='D'?item.is_correct==1?'layout-result-correct':'layout-result-error':item.correctOption=='D'?item.select_answer&&item.is_correct==2?'layout-result-correct':'':'')+' layout-result']}}" data-answer="D" data-event-opts="{{[['tap',[['singleSelectTap',['$event']]]]]}}" bindtap="__e"><text>D</text><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.D}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.D']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.E}}"><view class="{{[(item.select_answer=='E'?item.is_correct==1?'layout-result-correct':'layout-result-error':item.correctOption=='E'?item.select_answer&&item.is_correct==2?'layout-result-correct':'':'')+' layout-result']}}" data-answer="E" data-event-opts="{{[['tap',[['singleSelectTap',['$event']]]]]}}" bindtap="__e"><text>E</text><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.E}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.E']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.F}}"><view class="{{[(item.select_answer=='F'?item.is_correct==1?'layout-result-correct':'layout-result-error':item.correctOption=='F'?item.select_answer&&item.is_correct==2?'layout-result-correct':'':'')+' layout-result']}}" data-answer="F" data-event-opts="{{[['tap',[['singleSelectTap',['$event']]]]]}}" bindtap="__e"><text>F</text><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.F}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.F']]]]]}}" bindlongpress="__e"></rich-text></view></block></view></block><block wx:else><block wx:if="{{item.type==2}}"><view class="options-layout margin-top-xs" style="{{(cssData.questionContentCss)}}"><block wx:if="{{item.A}}"><view class="{{[(item.is_correct==0?$root.g0!=-1?'layout-result-select':'':item.is_correct==1?$root.g1?'layout-result-correct':'':$root.g2!=-1?$root.g3?'layout-result-correct':'layout-result-error':'')+' layout-result']}}" data-index="0" data-event-opts="{{[['tap',[['multipleSelectTap',['$event']]]]]}}" bindtap="__e"><text>A</text><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.A}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.A']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.B}}"><view class="{{[(item.is_correct==0?$root.g4!=-1?'layout-result-select':'':item.is_correct==1?$root.g5?'layout-result-correct':'':$root.g6!=-1?$root.g7?'layout-result-correct':'layout-result-error':'')+' layout-result']}}" data-index="1" data-event-opts="{{[['tap',[['multipleSelectTap',['$event']]]]]}}" bindtap="__e"><text>B</text><rich-text style="{{(cssData.questionContentCss)}}" nodes="{{item.B}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.B']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.C}}"><view class="{{[(item.is_correct==0?$root.g8!=-1?'layout-result-select':'':item.is_correct==1?$root.g9?'layout-result-correct':'':$root.g10!=-1?$root.g11?'layout-result-correct':'layout-result-error':'')+' layout-result']}}" data-index="2" data-event-opts="{{[['tap',[['multipleSelectTap',['$event']]]]]}}" bindtap="__e"><text>C</text><rich-text nodes="{{item.C}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.C']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.D}}"><view class="{{[(item.is_correct==0?$root.g12!=-1?'layout-result-select':'':item.is_correct==1?$root.g13?'layout-result-correct':'':$root.g14!=-1?$root.g15?'layout-result-correct':'layout-result-error':'')+' layout-result']}}" data-index="3" data-event-opts="{{[['tap',[['multipleSelectTap',['$event']]]]]}}" bindtap="__e"><text>D</text><rich-text nodes="{{item.D}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.D']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.E}}"><view class="{{[(item.is_correct==0?$root.g16!=-1?'layout-result-select':'':item.is_correct==1?$root.g17?'layout-result-correct':'':$root.g18!=-1?$root.g19?'layout-result-correct':'layout-result-error':'')+' layout-result']}}" data-index="4" data-event-opts="{{[['tap',[['multipleSelectTap',['$event']]]]]}}" bindtap="__e"><text>E</text><rich-text nodes="{{item.E}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.E']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.F}}"><view class="{{[(item.is_correct==0?$root.g20!=-1?'layout-result-select':'':item.is_correct==1?$root.g21?'layout-result-correct':'':$root.g22!=-1?$root.g23?'layout-result-correct':'layout-result-error':'')+' layout-result']}}" data-index="5" data-event-opts="{{[['tap',[['multipleSelectTap',['$event']]]]]}}" bindtap="__e"><text>F</text><rich-text nodes="{{item.F}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.F']]]]]}}" bindlongpress="__e"></rich-text></view></block><block wx:if="{{item.is_correct==0}}"><view data-event-opts="{{[['tap',[['multipleSelectCommitTap',['$event']]]]]}}" class="text-submit" style="{{(cssData.questionCommitCss)}}" bindtap="__e">提交</view></block></view></block></block><block wx:if="{{$root.g24>0}}"><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['openQuestionImagesTap',['$event']]]]]}}" class="cu-btn bg-blue" bindtap="__e"><text class="cuIcon-picfill" style="margin-right:10rpx;"></text>查看题目中的图片</button></view></block><block wx:if="{{!item.support_answer&&selectModelIndex==0&&item.is_correct<1}}"><view class="padding flex flex-direction"><button data-event-opts="{{[['tap',[['toggleUnsupportedAnswers',['$event']]]]]}}" class="cu-btn bg-blue" bindtap="__e"><text class="cuIcon-attentionfill" style="margin-right:10rpx;"></text>查看答案</button></view></block><block wx:if="{{item.is_correct>0}}"><view class="margin-buttom-xs" style="{{(cssData.questionAnswerCss)}}"><block wx:if="{{item.support_answer||selectModelIndex==1||item.is_correct>=1}}"><view class="explain-layout margin-top-xs"><block wx:if="{{item.is_correct>0}}"><view class="cu-bar bg-white" style="{{(cssData.questionOtherTitleCss)}}"><view class="action" style="margin-left:0;"><text class="cuIcon-titles text-blue" style="margin-right:0;"></text><text class="text-black">答案</text></view></view></block><block wx:if="{{item.is_correct>0}}"><view style="display:flex;"><view class="explain-answer" style="{{(cssData.questionContentCss)}}"><block wx:if="{{item.type==1||item.type==2||item.type==3}}"><view>参考答案</view></block><rich-text class="{{[item.type==1||item.type==2||item.type==3?'correct':'answer']}}" nodes="{{item.correctOption}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.correctOption']]]]]}}" bindlongpress="__e"></rich-text></view><block wx:if="{{item.type==1||item.type==2||item.type==3}}"><view class="explain-answer margin-left" style="{{(cssData.questionContentCss)}}"><view>您的答案</view><rich-text class="{{[item.is_correct==1?'correct':'error']}}" nodes="{{$root.g25?$root.g26:item.select_answer}}"></rich-text></view></block></view></block><view class="cu-bar bg-white" style="{{(cssData.questionOtherTitleCss)}}"><view class="action" style="margin-left:0;"><text class="cuIcon-titles text-blue" style="margin-right:0;"></text><text class="text-black">本题解析</text></view><view class="action"><button data-event-opts="{{[['tap',[['aiExplanationTap',[true]]]]]}}" class="cu-btn bg-orange round shadow" bindtap="__e"><text class="cuIcon-new" style="padding-right:2px;"></text>AI解题</button></view></view><block wx:if="{{item.explanation}}"><rich-text class="explain-text" style="{{(cssData.questionContentCss)}}" nodes="{{item.explanation}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['item.explanation']]]]]}}" bindlongpress="__e"></rich-text></block></view></block></view></block><view data-event-opts="{{[['tap',[['aiExplanationTap',[false]]]]]}}" class="{{['cu-modal','bottom-modal',openAiExplanationModal==true?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="cu-dialog" catchtap="__e"><view class="cu-bar bg-white justify-end"><view class="content"><text class="text-blod text-black">AI解题</text></view></view><view class="padding-xl" style="padding:30rpx;text-align:left;"><rich-text nodes="{{listData[swiperCurrent].aiExplanation}}" data-event-opts="{{[['longpress',[['onLongPress',['$0'],['listData.'+swiperCurrent+'.aiExplanation']]]]]}}" bindlongpress="__e"></rich-text></view></view></view><view data-event-opts="{{[['tap',[['onCloseSetTap',['$event']]]]]}}" class="{{['cu-modal','bottom-modal','',openSetModal==true?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="cu-dialog" catchtap="__e"><view class="cu-bar bg-white justify-between"><view class="content"><text class="text-bold text-black">工具箱</text></view><view data-event-opts="{{[['tap',[['onCloseSetTap',['$event']]]]]}}" class="action" bindtap="__e"><text class="cuIcon-close text-gray"></text></view></view><scroll-view class="bg-white nav" style="border-bottom:1rpx solid #f1f1f1;" scroll-x="{{true}}"><view class="flex text-center"><block wx:for="{{moreActionNavList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['cu-item','flex-sub',index==moreActionNavIndex?'text-blue cur':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['moreActionIndexTap',['$event']]]]]}}" bindtap="__e">{{''+item+''}}</view></block></view></scroll-view><view class="padding-bottom-xl"><block wx:if="{{moreActionNavIndex==0}}"><view class="padding-sm"><view class="display-card margin-bottom"><view class="display-card-header"><text class="cuIcon-favor text-blue margin-right-xs"></text><text class="text-bold">题目操作</text></view><view class="display-card-content"><view class="operation-grid"><view data-event-opts="{{[['tap',[['onCollectTap',['$event']]]]]}}" class="operation-btn" bindtap="__e"><view class="operation-icon"><text class="{{['cuIcon-likefill',listData[swiperCurrent]&&listData[swiperCurrent].isCollection==1?'text-red':'text-blue']}}"></text></view><view class="operation-text">{{listData[swiperCurrent]&&listData[swiperCurrent].isCollection==1?'取消收藏':'收藏题目'}}</view></view><view data-event-opts="{{[['tap',[['onFeedbackTap',['$event']]]]]}}" class="operation-btn" bindtap="__e"><view class="operation-icon"><text class="cuIcon-warnfill text-orange"></text></view><view class="operation-text">题目纠错</view></view><view data-event-opts="{{[['tap',[['onCleanErrorQuestionTap',['$event']]]]]}}" class="operation-btn" bindtap="__e"><view class="operation-icon"><text class="cuIcon-deletefill text-red"></text></view><view class="operation-text">清空错题</view></view><view data-event-opts="{{[['tap',[['onCleanQuestionTap',['$event']]]]]}}" class="operation-btn" bindtap="__e"><view class="operation-icon"><text class="cuIcon-warnfill text-red"></text></view><view class="operation-text">重置记录</view></view></view></view></view></view></block><block wx:if="{{moreActionNavIndex==1}}"><view class="padding-sm"><view class="display-card margin-bottom"><view class="display-card-header"><text class="cuIcon-forward text-blue margin-right-xs"></text><text class="text-bold">自动跳转设置</text></view><view class="display-card-content"><view class="cu-form-group"><view class="title">答题后自动跳转下一题</view><switch class="{{[isAutoNext?'checked':'']}}" checked="{{isAutoNext}}" data-event-opts="{{[['change',[['autoNextTap',['$event']]]]]}}" bindchange="__e"></switch></view><view class="cu-form-group"><view class="title">答题正确后自动跳转下一题</view><switch class="{{[isAutoCorrectNext?'checked':'']}}" checked="{{isAutoCorrectNext}}" data-event-opts="{{[['change',[['autoCorrectNextTap',['$event']]]]]}}" bindchange="__e"></switch></view></view></view></view></block><block wx:if="{{moreActionNavIndex==2}}"><view class="padding-sm"><view class="display-card margin-bottom"><view class="display-card-header"><text class="cuIcon-moon text-yellow margin-right-xs"></text><text class="text-bold">夜间模式</text></view><view class="display-card-content flex align-center justify-between"><view class="text-gray text-sm">保护眼睛，适合夜间阅读</view><switch checked="{{isDarkMode}}" color="#39b54a" data-event-opts="{{[['change',[['toggleDarkMode',['$event']]]]]}}" bindchange="__e"></switch></view></view><view class="display-card"><view class="display-card-header"><text class="cuIcon-font text-blue margin-right-xs"></text><text class="text-bold">字体大小</text></view><view class="display-card-content"><view class="text-gray text-sm margin-bottom-sm">选择适合您的字体大小</view><view class="font-size-slider"><block wx:for="{{selectFontList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['setQuestionFontTap',[['o',['detail',{value:item.value}]]]]]]]}}" class="{{['font-size-item',item.value==questionFont?'active':'']}}" bindtap="__e"><text style="{{('font-size:'+item.value)}}">A</text><view class="font-size-dot"></view></view></block></view><view class="font-size-labels flex justify-between"><text class="text-xs text-gray">小</text><text class="text-xs text-gray">默认</text><text class="text-xs text-gray">大</text></view></view></view></view></block></view></view></view><view class="bottom-layout cu-bar tabbar"><view data-event-opts="{{[['tap',[['onOpenSetTap',['$event']]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-usefullfill"></view>工具</view><view data-event-opts="{{[['tap',[['setQuestionDirectionTap',[1]]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-roundleftfill-copy"></view>上一题</view><view data-event-opts="{{[['tap',[['commitTap',['$event']]]]]}}" class="action text-blue add-action" bindtap="__e"><button class="cu-btn cuIcon-write shadow bg-gradual-blue"></button>保存</view><view class="action text-blue" data-type="2" data-event-opts="{{[['tap',[['setQuestionDirectionTap',[2]]]]]}}" bindtap="__e"><view class="cuIcon-roundrightfill"></view>下一题</view><view data-event-opts="{{[['tap',[['openAnswerCardTap',['$event']]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-formfill"></view>答题卡</view></view></view><view data-event-opts="{{[['tap',[['closeAnswerCardTap',['$event']]]]]}}" class="{{['cu-modal','bottom-modal',openAnswerCardModal?'show':'']}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="cu-dialog answer-card-dialog" catchtap="__e"><view class="cu-bar bg-white justify-between"><view class="content"><text class="text-blod text-black">答题卡</text></view><view data-event-opts="{{[['tap',[['closeAnswerCardTap',['$event']]]]]}}" class="action" bindtap="__e"><text class="cuIcon-close text-gray"></text></view></view><view class="padding-sm bg-white flex align-center justify-between" style="border-bottom:1rpx solid #eee;"><view class="flex align-center"><view><text class="text-bold text-lg">已答：<text class="text-blue">{{countStats[2]}}</text>/<text>{{questionCount}}</text></text></view></view><view class="flex align-center"><view class="margin-right-sm flex align-center"><view class="cu-tag bg-green round margin-right-xs" style="width:40rpx;height:40rpx;padding:0;box-shadow:0 2rpx 4rpx rgba(0,0,0,0.1);display:flex;align-items:center;justify-content:center;"><text class="cuIcon-check text-white" style="font-size:24rpx;"></text></view><text class="text-green">{{"正确("+countStats[0]+")"}}</text></view><view class="margin-right-sm flex align-center"><view class="cu-tag bg-red round margin-right-xs" style="width:40rpx;height:40rpx;padding:0;box-shadow:0 2rpx 4rpx rgba(0,0,0,0.1);display:flex;align-items:center;justify-content:center;"><text class="cuIcon-close text-white" style="font-size:24rpx;"></text></view><text class="text-red">{{"错误("+countStats[1]+")"}}</text></view><view class="flex align-center"><view class="cu-tag bg-white round margin-right-xs" style="width:40rpx;height:40rpx;padding:0;box-shadow:0 2rpx 4rpx rgba(0,0,0,0.1);border:1px solid #e0e0e0;"></view><text class="text-black">{{"未答("+countStats[3]+")"}}</text></view></view></view><scroll-view class="answer-card-scroll-view" scroll-y="{{true}}" show-scrollbar="true" enhanced="true" scroll-top="0"><view class="padding-sm bg-white answer-card-content"><view style="display:grid;grid-template-columns:repeat(6, 1fr);gap:15rpx;padding:10rpx 0;"><block wx:for="{{answerSheetStatus}}" wx:for-item="item" wx:for-index="__i0__" wx:key="index"><button data-event-opts="{{[['tap',[['answerCardItemTap',['$0'],[[['answerSheetStatus','index',item.index]]]]]]]}}" class="{{['cu-btn round answer-card-btn',item.status===1?'bg-green text-white':item.status===2?'bg-red text-white':'bg-white text-black']}}" bindtap="__e">{{''+item.index+''}}</button></block></view></view></scroll-view></view></view></view>