rich-text {
	line-height: 40rpx;
}

/* 暗黑模式变量 */
.dark-mode {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --highlight-color: #4c9eff;
    --secondary-bg: #2c2c2c;
    --secondary-text: #aaaaaa;
    --disabled-color: #777777;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* 应用暗黑模式 */
.app-container {
    min-height: 100vh;
}
.app-container.dark-mode {
    background-color: var(--bg-color);
    color: var(--text-color);
}
.app-container.dark-mode .nav {
    background-color: var(--card-bg);
    border-bottom: 1rpx solid var(--border-color);
}
.app-container.dark-mode .nav .cu-item {
    color: var(--text-color);
}
.app-container.dark-mode .nav .cu-item.cur {
    color: var(--highlight-color);
}
.app-container.dark-mode .questionAsk-layout {
    background-color: var(--card-bg);
}
.app-container.dark-mode .question-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}
.app-container.dark-mode .options-layout .layout-result {
    background-color: var(--secondary-bg);
    color: var(--text-color);
    border: 1rpx solid var(--border-color);
}

/* 夜间模式下选项状态样�?*/
.app-container.dark-mode .layout-result-correct {
    background-color: #2c6e45 !important; /* 深绿�?*/
    color: #ffffff !important;
    border: 1px solid #3a8c59 !important;
}
.app-container.dark-mode .layout-result-error {
    background-color: #8c3a3a !important; /* 深红�?*/
    color: #ffffff !important;
    border: 1px solid #a14747 !important;
}
.app-container.dark-mode .layout-result-select {
    background-color: #324b7a !important; /* 深蓝�?*/
    color: #ffffff !important;
    border: 1px solid #3e5c94 !important;
}
.app-container.dark-mode .bottom-layout {
    background-color: var(--card-bg);
    box-shadow: 0 -2rpx 6rpx var(--shadow-color);
    border-top: 1rpx solid var(--border-color);
}

/* 底部导航栏样�?*/
.bottom-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
    background-color: #ffffff;
    box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid #eee;
}
.bottom-layout .action {
    color: #333333; /* 普通按钮颜�?*/
}
.bottom-layout .action.text-blue {
    color: #0081ff; /* 蓝色按钮颜色 */
}
.app-container.dark-mode .bottom-layout .action {
    color: var(--text-color);
}
.app-container.dark-mode .bottom-layout .action.text-blue {
    color: var(--highlight-color);
}
.app-container.dark-mode .cu-btn.shadow {
    background-color: #444444;
}
.cu-btn.shadow {
    background-color: #0081ff; /* 蓝色按钮 */
}
.app-container.dark-mode .display-card {
    background-color: var(--card-bg);
    border: 1rpx solid var(--border-color);
}
.app-container.dark-mode .display-card-header {
    border-bottom: 1rpx solid var(--border-color);
}
.app-container.dark-mode .operation-btn {
    background-color: #2d2d2d;
    color: var(--text-color);
    border: 1px solid #444444;
}
.app-container.dark-mode .operation-text {
    color: var(--text-color);
    font-weight: 500;
}
.app-container.dark-mode .operation-icon {
    color: var(--highlight-color);
}
.app-container.dark-mode .cu-dialog {
    background-color: var(--card-bg);
    color: var(--text-color);
}
.app-container.dark-mode .cu-bar.bg-white {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* 夜间模式下答题卡样式 */
.app-container.dark-mode .cu-dialog .bg-white {
    background-color: var(--card-bg) !important;
    color: var(--text-color);
}
.app-container.dark-mode .text-green {
    color: #4cd964 !important;
}
.app-container.dark-mode .text-red {
    color: #ff6b6b !important;
}
.app-container.dark-mode .text-gray {
    color: #aaaaaa !important;
}

/* 夜间模式下表单组样式 */
.app-container.dark-mode .cu-form-group {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}
.app-container.dark-mode .cu-form-group .title {
    color: var(--text-color);
}

/* 夜间模式下开关样�?*/
.app-container.dark-mode switch {
    background-color: #444 !important;
}
.app-container.dark-mode switch.checked {
    background-color: var(--highlight-color) !important;
}
.questionAsk-layout {
	display: flex;
	flex-direction: column;
}
.questionAsk-layout .nosupport {
	color: #999;
	margin-top: 30rpx;
}
.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	padding-bottom: 30rpx;
}
.bottom-layout {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}
.options-layout rich-text {
	margin-left: 30rpx;
}
.layout-result {
	margin: 30rpx 30rpx 0 30rpx;
	padding: 30rpx 30rpx 30rpx 40rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	color: #333;
	border-radius: 20rpx;
}
.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}
.layout-result-error {
	display: flex;
	align-items: center;
	background: #fd7d7f;
	color: white;
}
.layout-result-select {
	display: flex;
	align-items: center;
	background: rgb(127, 168, 243);
	color: white;
}
.text-submit {
	text-align: center;
	border-radius: 20rpx;
	margin-top: 30rpx;
	margin-left: 30%;
	margin-right: 30%;
}
.explain-layout {
	padding: 30rpx;
	padding-bottom: 80rpx; /* 增加底部边距，避免被凸起的保存按钮遮�?*/
}
.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}
.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}





























































































































































































































































































































































































































































































































































/* 显示设置卡片样式 */

/* 显示设置卡片样式 */
.display-card {
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
    overflow: hidden;
    margin: 20rpx 0;
}
.display-card-header {
    padding: 24rpx;
    border-bottom: 1rpx solid #f5f5f5;
    display: flex;
    align-items: center;
}
.display-card-content {
    padding: 24rpx;
}

/* 字体大小选择器样式 */
.font-size-slider {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    position: relative;
}
.font-size-slider:after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    height: 4rpx;
    background-color: #eee;
    z-index: 1;
}
.font-size-item {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    background-color: #fff;
}
.font-size-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: #ddd;
    margin-top: 8rpx;
}
.font-size-item.active text {
    color: #0081ff;
    font-weight: bold;
}
.font-size-item.active .font-size-dot {
    background-color: #0081ff;
    width: 24rpx;
    height: 24rpx;
}
.font-size-labels {
    margin-top: 10rpx;
}

/* 操作按钮样式 */
.operation-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.operation-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    background-color: #f8f8f8;
    transition: all 0.3s;
    width: 48%;
    box-sizing: border-box;
}
.operation-btn:active {
    background-color: #e0e0e0;
}
.operation-icon {
    font-size: 48rpx;
    margin-bottom: 10rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}
.operation-text {
    font-size: 26rpx;
    color: #333;
    text-align: center;
}

/* 开关样式优化 */
.cu-switch {
    position: relative;
    width: 90rpx;
    height: 50rpx;
    border-radius: 100rpx;
    background-color: #ddd;
    transition: background-color 0.3s;
}
.cu-switch::before {
    content: "";
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 0 10rpx rgba(0,0,0,0.1);
    left: 5rpx;
    top: 5rpx;
    transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    transition: transform 0.3s, -webkit-transform 0.3s;
}
.cu-switch.checked {
    background-color: #39b54a;
}
.cu-switch.checked::before {
    -webkit-transform: translateX(40rpx);
            transform: translateX(40rpx);
}

/* 答题卡弹窗样式 */
.answer-card-dialog {
    max-height: 80vh !important; /* 限制最大高度为视口高度的80% */
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
}
.answer-card-scroll-view {
    flex: 1;
    height: auto;
    min-height: 200rpx; /* 设置最小高度确保内容可见 */
    max-height: 60vh; /* 限制滚动区域的最大高度 */
    overflow-y: auto;
    position: relative;
    -webkit-overflow-scrolling: touch; /* 增加iOS滚动流畅度 */
}
.answer-card-content {
    padding-bottom: 30rpx !important; /* 确保底部有足够的空间 */
}
.answer-card-btn {
    width: 76rpx; 
    height: 76rpx; 
    margin: 5rpx auto; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    font-size: 32rpx; 
    font-weight: 500; 
    padding: 0; 
    box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1); 
    transition: all 0.2s ease; 
    border: 1px solid #e0e0e0;
}

/* 修复弹窗显示问题 */
.cu-modal.show {
    opacity: 1;
    transition-duration: 0.3s;
    overflow-y: visible;
    display: flex !important;
    align-items: flex-end;
}
.cu-modal.bottom-modal.show .cu-dialog {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    transition-duration: 0.3s;
    overflow: visible;
}

