<view class="{{['data-v-4f69b280',responsiveClasses]}}"><back vue-id="311a3246-1" showBackText="{{false}}" showBackIcon="{{false}}" showBackLeft="{{false}}" showHomeIcon="{{false}}" customClass="bg-gradual-blue text-white" title="响应式演示" class="data-v-4f69b280" bind:__l="__l"></back><block wx:if="{{showDebug}}"><view class="debug-info data-v-4f69b280"><text class="responsive-text-sm data-v-4f69b280">{{"断点: "+responsive.breakpoint}}</text><text class="responsive-text-sm data-v-4f69b280">{{"布局: "+responsive.layoutMode}}</text><text class="responsive-text-sm data-v-4f69b280">{{"列数: "+responsive.gridCols}}</text><text class="responsive-text-sm data-v-4f69b280">{{"侧边栏: "+(responsive.showSidebar?'显示':'隐藏')}}</text><button data-event-opts="{{[['tap',[['toggleDebug',['$event']]]]]}}" class="debug-toggle data-v-4f69b280" bindtap="__e">隐藏调试</button></view></block><block wx:else><button data-event-opts="{{[['tap',[['toggleDebug',['$event']]]]]}}" class="debug-toggle data-v-4f69b280" bindtap="__e">显示调试</button></block><responsive-container vue-id="311a3246-2" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{responsive.showSidebar}}"><view class="responsive-flex data-v-4f69b280"><view class="responsive-sidebar responsive-padding-base data-v-4f69b280"><view class="sidebar-section data-v-4f69b280"><text class="responsive-text-lg text-bold text-blue data-v-4f69b280">导航菜单</text><view class="sidebar-menu responsive-margin-base data-v-4f69b280"><block wx:for="{{menuItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sidebar-menu-item data-v-4f69b280"><text class="{{['text-blue','data-v-4f69b280',item.icon]}}"></text><text class="responsive-text-base data-v-4f69b280">{{item.name}}</text></view></block></view></view></view><view class="responsive-main responsive-padding-base data-v-4f69b280"><view class="desktop-content data-v-4f69b280"><text class="responsive-text-2xl text-bold margin-bottom data-v-4f69b280">桌面布局</text><responsive-grid vue-id="{{('311a3246-3')+','+('311a3246-2')}}" cols="{{({xs:1,sm:2,md:3,lg:4,xl:5,xxl:6})}}" gap="{{20}}" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{cardItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><responsive-card vue-id="{{('311a3246-4-'+index)+','+('311a3246-3')}}" clickable="{{true}}" data-event-opts="{{[['^tap',[['handleCardTap',['$0'],[[['cardItems','',index]]]]]]]}}" bind:tap="__e" class="data-v-4f69b280" bind:__l="__l" vue-slots="{{['default']}}"><view class="card-content data-v-4f69b280"><text class="{{['card-icon','text-blue','data-v-4f69b280',item.icon]}}"></text><text class="card-title responsive-text-base data-v-4f69b280">{{item.title}}</text><text class="card-desc responsive-text-sm text-gray data-v-4f69b280">{{item.desc}}</text></view></responsive-card></block></responsive-grid></view></view></view></block><block wx:else><view class="mobile-content responsive-padding-base data-v-4f69b280"><text class="responsive-text-2xl text-bold margin-bottom data-v-4f69b280">移动端布局</text><view class="responsive-list data-v-4f69b280"><block wx:for="{{cardItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><responsive-card class="responsive-list-item data-v-4f69b280" vue-id="{{('311a3246-5-'+index)+','+('311a3246-2')}}" clickable="{{true}}" data-event-opts="{{[['^tap',[['handleCardTap',['$0'],[[['cardItems','',index]]]]]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="list-item-content data-v-4f69b280"><text class="{{['list-icon','text-blue','data-v-4f69b280',item.icon]}}"></text><view class="list-info data-v-4f69b280"><text class="list-title responsive-text-base data-v-4f69b280">{{item.title}}</text><text class="list-desc responsive-text-sm text-gray data-v-4f69b280">{{item.desc}}</text></view></view></responsive-card></block></view></view></block></responsive-container></view>