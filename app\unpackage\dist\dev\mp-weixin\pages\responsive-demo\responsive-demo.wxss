
.debug-info.data-v-4f69b280 {
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.debug-toggle.data-v-4f69b280 {
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  z-index: 9999;
  background: #007aff;
  color: white;
  border: none;
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
}
.sidebar-menu-item.data-v-4f69b280 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}
.sidebar-menu-item text.data-v-4f69b280:first-child {
  margin-right: 15rpx;
  font-size: 32rpx;
}
.card-content.data-v-4f69b280 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.card-icon.data-v-4f69b280 {
  font-size: 60rpx;
  margin-bottom: 15rpx;
}
.card-title.data-v-4f69b280 {
  margin-bottom: 10rpx;
  font-weight: bold;
}
.list-item-content.data-v-4f69b280 {
  display: flex;
  align-items: center;
}
.list-icon.data-v-4f69b280 {
  font-size: 50rpx;
  margin-right: 20rpx;
}
.list-info.data-v-4f69b280 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.list-title.data-v-4f69b280 {
  margin-bottom: 8rpx;
  font-weight: bold;
}
.margin-bottom.data-v-4f69b280 {
  margin-bottom: 30rpx;
}

