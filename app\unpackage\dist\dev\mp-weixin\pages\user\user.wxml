<view class="{{[responsiveClasses]}}"><block wx:if="{{load}}"><view class="responsive-container"><block wx:if="{{responsive.showSidebar}}"><view class="responsive-flex"><view class="responsive-sidebar responsive-padding-base"><view class="sidebar-user-info"><view class="cu-avatar xl round" style="{{('background-image:url('+user.avatar+');')}}"><block wx:if="{{user.is_vip==1}}"><view class="cu-tag badge bg-yellow cuIcon-vip"></view></block></view><view class="user-name responsive-text-lg margin-top">{{user.nickname}}</view><view class="user-sign responsive-text-sm margin-top-sm">相信美好的事情即将发生</view></view><view class="sidebar-stats responsive-margin-base"><view class="stat-item"><view class="stat-value responsive-text-xl text-orange">{{user.id}}</view><view class="stat-label responsive-text-sm">编号</view></view><view class="stat-item"><view class="stat-value responsive-text-xl text-blue">{{user.vip_name}}</view><view class="stat-label responsive-text-sm">级别</view></view></view></view><view class="responsive-main responsive-padding-base"><view class="desktop-user-layout"><view class="responsive-grid"><block wx:for="{{menuList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="responsive-grid-item"><view class="user-menu-card responsive-card" data-url="{{item.url}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><text class="{{['menu-icon',item.icon]}}"></text><text class="menu-text responsive-text-base">{{item.name}}</text></view></view></block></view></view></view></view></block><block wx:else><view class="mobile-user-layout"><view class="UCenter-bg" style="{{('background-image: url('+user.pageConfig.background_image+');background-size: cover;')}}" data-url="{{paths.info}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="cu-avatar xl round" style="{{('background-image:url('+user.avatar+');')}}"><block wx:if="{{user.is_vip==1}}"><view class="cu-tag badge bg-yellow cuIcon-vip"></view></block></view><view class="text-xl margin-top"><text>{{"你好，"+user.nickname}}</text></view><view class="margin-top-sm"><text class="sign">相信美好的事情即将发生</text></view><image class="gif-wave" mode="scaleToFill" src="https://learnfile.20230611.cn/learnAppClient/cb/de/cbde6f5e83388a096d28fab7339d56fd.gif"></image></view><view class="padding flex text-center text-grey bg-white shadow-warp"><view data-event-opts="{{[['tap',[['copyTap',['$0'],['user.id']]]]]}}" class="flex flex-sub flex-direction solid-right" bindtap="__e"><view class="text-xxl text-orange">{{user.id}}</view><view class="margin-top-sm"><text class="cuIcon-cuIcon"></text>编号</view></view><view class="flex flex-sub flex-direction solid-right" data-url="{{paths.rechargeMember}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="text-xxl text-blue">{{user.vip_name}}</view><view class="margin-top-sm"><text class="cuIcon-peoplefill"></text>级别</view></view><view class="flex flex-sub flex-direction" data-url="{{paths.score}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><view class="text-xxl text-green">{{user.score}}</view><view class="margin-top-sm"><text class="cuIcon-rechargefill"></text>积分</view></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>账户管理</view></view><view class="cu-list grid col-5 no-border"><block wx:if="{{!user.openid}}"><view class="cu-item" data-url="{{paths.wechat}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/wechat.png"></image><text>绑定微信</text></view></block><block wx:if="{{!appIsAudit&&!user.email}}"><view class="cu-item" data-url="{{paths.email}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/email.png"></image><text>绑定邮箱</text></view></block><block wx:if="{{!appIsAudit&&!user.mobile}}"><view class="cu-item" data-url="{{paths.mobile}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/phone.png"></image><text>绑定手机</text></view></block><view class="cu-item" data-url="{{paths.password}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/password.png"></image><text>设置密码</text></view><view class="cu-item" data-url="{{paths.info}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/user-male-circle.png"></image><text>个人资料</text></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>学习管理</view></view><view class="cu-list grid col-5 no-border"><view class="cu-item" data-url="{{paths.myCourse}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/book.png"></image><text>我的题库</text></view><view class="cu-item" data-url="{{paths.myErrorQuestion}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/error.png"></image><text>我的错题</text></view><view class="cu-item" data-url="{{paths.myCollect}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/bookmark.png"></image><text>我的收藏</text></view></view><view class="cu-bar bg-white solid-bottom margin-top-xs"><view class="action"><text class="text-orange"></text>我的服务</view></view><view class="cu-list grid col-5 no-border"><block wx:if="{{!appIsAudit}}"><view class="cu-item" data-url="{{paths.about}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/about.png"></image><text>关于我们</text></view></block><block wx:if="{{appPlatform==20||appPlatform==21}}"><view data-event-opts="{{[['tap',[['showOnlineServiceTap']]]]}}" class="cu-item" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/customer-support.png"></image><text>在线客服</text></view></block><view class="cu-item" data-url="{{paths.opinion}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/feeback.png"></image><text>意见反馈</text></view><block wx:if="{{user.is_agent==1}}"><view class="cu-item" data-url="{{paths.promote}}" data-event-opts="{{[['tap',[['menuTap',['$event']]]]]}}" bindtap="__e"><image class="menu-image" style="width:100%;" mode="aspectFit" src="/static/img/share.png"></image><text>推广中心</text></view></block></view></view></block><confirm vue-id="380011e0-1" title="接入提醒" content="在线客服属于兼职，回复可能会不及时，请先留言，最晚会在12小时内回复您。" status="{{showOnlineServiceNotice}}" confirmText="知道啦，我要接入客服" confirmTextClass="cuIcon-servicefill" confirmButtonOpenType="contact" data-event-opts="{{[['^updateStatus',[['__set_sync',['$0','showOnlineServiceNotice','$event'],['']]]]]}}" bind:updateStatus="__e" bind:__l="__l"></confirm></view></block></view>