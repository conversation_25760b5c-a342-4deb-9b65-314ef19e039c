<hybrid-view vue-id="0f48ec5a-1" web-url="{{hybridWebUrl}}" force-mode="{{hybridMode}}" url-params="{{hybridParams}}" data-event-opts="{{[['^modeChange',[['handleModeChange']]],['^webMessage',[['handleWebMessage']]]]}}" bind:modeChange="__e" bind:webMessage="__e" class="data-v-b62bacda" bind:__l="__l" vue-slots="{{['default']}}"><view class="native-fallback data-v-b62bacda"><back vue-id="{{('0f48ec5a-2')+','+('0f48ec5a-1')}}" showBackText="{{false}}" showBackIcon="{{true}}" showBackLeft="{{true}}" showHomeIcon="{{false}}" customClass="bg-gradual-blue text-white" title="加载中" class="data-v-b62bacda" bind:__l="__l"></back><view class="loading-content data-v-b62bacda"><view class="loading-text data-v-b62bacda">正在加载Web端内容...</view></view></view></hybrid-view>