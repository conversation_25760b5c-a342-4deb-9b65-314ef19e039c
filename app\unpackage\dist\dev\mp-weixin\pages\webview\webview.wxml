<hybrid-view vue-id="9b069568-1" web-url="{{webUrl}}" force-mode="{{forceMode}}" debug-mode="{{true}}" url-params="{{urlParams}}" data-event-opts="{{[['^modeChange',[['handleModeChange']]],['^webMessage',[['handleWebMessage']]],['^webLoad',[['handleWebLoad']]],['^webError',[['handleWebError']]]]}}" bind:modeChange="__e" bind:webMessage="__e" bind:webLoad="__e" bind:webError="__e" class="data-v-baadaa0c" bind:__l="__l" vue-slots="{{['default']}}"><view class="native-content data-v-baadaa0c"><back vue-id="{{('9b069568-2')+','+('9b069568-1')}}" showBackText="{{false}}" showBackIcon="{{true}}" showBackLeft="{{true}}" showHomeIcon="{{false}}" customClass="bg-gradual-blue text-white" title="混合模式" class="data-v-baadaa0c" bind:__l="__l"></back><view class="content-wrapper data-v-baadaa0c"><view class="mode-info data-v-baadaa0c"><text class="mode-title data-v-baadaa0c">{{"当前模式："+currentMode}}</text><text class="mode-desc data-v-baadaa0c">{{modeDescription}}</text></view><view class="control-panel data-v-baadaa0c"><button data-event-opts="{{[['tap',[['switchMode',['auto']]]]]}}" class="cu-btn bg-blue margin-sm data-v-baadaa0c" bindtap="__e">自动模式</button><button data-event-opts="{{[['tap',[['switchMode',['web']]]]]}}" class="cu-btn bg-green margin-sm data-v-baadaa0c" bindtap="__e">强制Web端</button><button data-event-opts="{{[['tap',[['switchMode',['native']]]]]}}" class="cu-btn bg-orange margin-sm data-v-baadaa0c" bindtap="__e">强制原生</button></view><view class="info-panel data-v-baadaa0c"><view class="info-item data-v-baadaa0c"><text class="info-label data-v-baadaa0c">Web URL:</text><text class="info-value data-v-baadaa0c">{{webUrl}}</text></view><view class="info-item data-v-baadaa0c"><text class="info-label data-v-baadaa0c">屏幕尺寸:</text><text class="info-value data-v-baadaa0c">{{screenSize}}</text></view></view><view class="native-features data-v-baadaa0c"><text class="feature-title data-v-baadaa0c">原生小程序功能</text><view class="feature-list data-v-baadaa0c"><view data-event-opts="{{[['tap',[['goToPage',['/pages/index/index']]]]]}}" class="feature-item data-v-baadaa0c" bindtap="__e"><text class="cuIcon-home data-v-baadaa0c"></text><text class="data-v-baadaa0c">首页</text></view><view data-event-opts="{{[['tap',[['goToPage',['/pages/search/search']]]]]}}" class="feature-item data-v-baadaa0c" bindtap="__e"><text class="cuIcon-search data-v-baadaa0c"></text><text class="data-v-baadaa0c">搜索</text></view><view data-event-opts="{{[['tap',[['goToPage',['/pages/user/user']]]]]}}" class="feature-item data-v-baadaa0c" bindtap="__e"><text class="cuIcon-people data-v-baadaa0c"></text><text class="data-v-baadaa0c">我的</text></view></view></view></view></view></hybrid-view>