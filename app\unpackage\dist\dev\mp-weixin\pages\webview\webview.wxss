
.native-content.data-v-baadaa0c {
  width: 100%;
  height: 100vh;
  background: #f8f9fa;
}
.content-wrapper.data-v-baadaa0c {
  padding: 20rpx;
}
.mode-info.data-v-baadaa0c {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.mode-title.data-v-baadaa0c {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.mode-desc.data-v-baadaa0c {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.control-panel.data-v-baadaa0c {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
  text-align: center;
}
.info-panel.data-v-baadaa0c {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.info-item.data-v-baadaa0c {
  margin-bottom: 20rpx;
}
.info-label.data-v-baadaa0c {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}
.info-value.data-v-baadaa0c {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
  display: block;
}
.native-features.data-v-baadaa0c {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.feature-title.data-v-baadaa0c {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.feature-list.data-v-baadaa0c {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.feature-item.data-v-baadaa0c {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}
.feature-item text.data-v-baadaa0c:first-child {
  font-size: 40rpx;
  color: #007aff;
}
.feature-item text.data-v-baadaa0c:last-child {
  font-size: 24rpx;
  color: #333;
}

