# PC端Web端显示测试

## 🎯 目标
在PC端微信小程序中，自动显示Web端页面而不是原生小程序内容。

## 🔧 检测逻辑
现在使用多重检测确保PC端显示Web端：

1. **平台检测**: `platform === 'windows'` 或 `platform === 'mac'`
2. **系统检测**: `system` 包含 'Windows' 或 'Mac'
3. **屏幕宽度**: `windowWidth > 1000` (PC端特征)
4. **大屏检测**: `windowWidth > 500` 或 `aspectRatio > 0.8`
5. **微信PC端**: `windowWidth > 800` 且 `aspectRatio > 1.0`

## 📱 测试方法

### 方法1: 正常测试
在PC端微信中打开小程序，应该自动显示Web端

### 方法2: 强制测试
在首页URL后添加参数: `?forceWeb=true`
例如: `/pages/index/index?forceWeb=true`

### 方法3: 查看控制台
打开开发者工具，查看控制台输出的屏幕信息

## 🌐 Web端URL
- 首页: https://www.beikeshuati.com/
- 搜索: https://www.beikeshuati.com/search  
- 练习: https://www.beikeshuati.com/practice

## ⚠️ 注意事项
1. 确保域名 `www.beikeshuati.com` 已在小程序后台配置
2. Web端必须支持HTTPS
3. 检查控制台日志确认检测结果

## 🐛 调试信息
控制台会输出详细的检测信息：
```
屏幕信息: {
  windowWidth: 1200,
  windowHeight: 800, 
  platform: "windows",
  system: "Windows 10",
  aspectRatio: 1.5,
  isPC: true,
  isLargeScreen: true,
  isWeChatPC: true,
  shouldShowWeb: true
}
```

如果 `shouldShowWeb` 为 `true` 但仍显示原生内容，请检查Web端URL是否可访问。
